package com.unicom.swdx.module.system.service.sms;

import com.unicom.swdx.framework.sms.core.client.SmsCommonResult;
import com.unicom.swdx.framework.sms.core.client.dto.SmsSendRespDTO;
import com.unicom.swdx.module.system.enums.sms.SmsVerificationTypeEnum;
import com.unicom.swdx.module.system.mq.message.sms.SmsSendMessage;
import com.unicom.swdx.module.system.enums.sms.SmsMessageEnum;

import java.util.Map;

/**
 * 短信发送 Service 接口
 *
 * <AUTHOR>
 */
public interface SmsSendService {

    /**
     * 发送单条短信给管理后台的用户
     *
     * 在 mobile 为空时，使用 userId 加载对应管理员的手机号
     *
     * @param mobile 手机号
     * @param userId 用户编号
     * @param templateId 短信模板编号
     * @param templateParams 短信模板参数
     * @return 发送日志编号
     */
    Long sendSingleSmsToAdmin(String mobile, Long userId,
                              String templateId, Map<String, Object> templateParams);

    /**
     * 发送单条短信给管理后台的用户
     *
     * 在 mobile 为空时，使用 userId 加载对应管理员的手机号
     *
     * @param mobile 手机号
     * @param userId 用户编号
     * @param msg 短信枚举
     * @return 发送日志编号
     */
    void sendSingleSmsToAdminDX(String mobile, Long userId, String msg);

    /**
     * 发送单条短信给用户 APP 的用户
     *
     * 在 mobile 为空时，使用 userId 加载对应会员的手机号
     *
     * @param mobile 手机号
     * @param userId 用户编号
     * @param templateCode 短信模板编号
     * @param templateParams 短信模板参数
     * @return 发送日志编号
     */
//    Long sendSingleSmsToMember(String mobile, Long userId,
//                               String templateCode, Map<String, Object> templateParams);

    /**
     * 发送单条短信给用户
     *
     * @param mobile 手机号
     * @param userId 用户编号(非必填)
     * @param templateId 短信模板id
     * @param templateParams 短信模板参数
     * @return 发送日志编号
     */
    Long sendSingleSms(String mobile, Long userId, Integer userType,
                       String templateCode, Map<String, Object> templateParams);


    SmsCommonResult<SmsSendRespDTO> sendSingleSmss(String mobile, Long userId, Integer userType,
                       String templateCode, Map<String, Object> templateParams);

    void sendSingleSmsApp(String mobile, String msg);

    /**
     * 接收短信的接收结果
     *
     * @param channelCode 渠道编码
     * @param text 结果内容
     * @throws Throwable 处理失败时，抛出异常
     */
    void receiveSmsStatus(String channelCode, String text) throws Throwable;

    void sendSms(Map<String, Object> body,Long smsLogId);

    void doSendSms(SmsSendMessage message);


    SmsCommonResult<SmsSendRespDTO> doSendSmss(SmsSendMessage message);

    void getVerification(String mobile, SmsMessageEnum verificationTypeEnum);
    void getVerificationYXUnit(String mobile, SmsMessageEnum verificationTypeEnum);


}

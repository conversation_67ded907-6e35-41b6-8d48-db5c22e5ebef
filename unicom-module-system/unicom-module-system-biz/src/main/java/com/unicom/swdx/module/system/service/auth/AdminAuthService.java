package com.unicom.swdx.module.system.service.auth;

import com.unicom.swdx.module.system.controller.admin.auth.vo.*;
import com.unicom.swdx.module.system.controller.xcx.auth.vo.WxXcxAuthLoginReqVO;
import com.unicom.swdx.module.system.controller.xcx.auth.vo.WxXcxAuthLoginRespVO;
import com.unicom.swdx.module.system.dal.dataobject.user.AdminUserDO;
import com.unicom.swdx.module.system.enums.logger.LoginLogTypeEnum;

import javax.validation.Valid;
import java.util.List;

/**
 * 管理后台的认证 Service 接口
 *
 * 提供用户的登录、登出的能力
 *
 * <AUTHOR>
 */
public interface AdminAuthService {

    /**
     * 账号登录
     *
     * @param reqVO 登录信息
     * @return 登录结果
     */
    AuthLoginRespVO login(@Valid AuthLoginReqVO reqVO);

    /**
     * 基于 token 退出登录
     *
     * @param token token
     * @param logType 登出类型
     */
    void logout(String token, Integer logType);

    /**
     * 刷新访问令牌
     *
     * @param refreshToken 刷新令牌
     * @return 登录结果
     */
    AuthLoginRespVO refreshToken(String refreshToken);

    /**
     * 获取用户的权限信息
     * @return 用户的权限信息
     */
    AuthPermissionInfoRespVO getPermissionInfo(Long clientId);

    /**
     * 根据应用id获取菜单信息
     * @param clientId 应用id
     * @return 菜单信息
     */
    List<AuthMenuRespVO> getMenusByClient(Long clientId);

    /**
     * 手机验证码登录
     * @param reqVO 登录信息
     * @return 登录结果
     */
    AuthLoginRespVO codeLogin(@Valid CodeAuthLoginReqVO reqVO);

    /**
     * 小程序登录
     * @param loginReqVO 登录信息
     * @return 登录结果
     */
    AuthLoginRespVO wxXcxLogin(WxXcxLoginReqVO loginReqVO);


    /* --------------------------------------- */

    /**
     * 验证账号 + 密码。如果通过，则返回用户
     *
     * @param username 账号
     * @param password 密码
     * @return 用户
     */
    AdminUserDO authenticate(String username, String password, String type);

    /**
     * 调训单位登录 验证账号 + 密码。如果通过，则返回用户
     *
     * @param username 账号
     * @param password 密码
     * @return 用户
     */
    AdminUserDO authenticateForUnit(String username, String password, String type);

    /**
     * 社交快捷登录，使用 code 授权码
     *
     * @param reqVO 登录信息
     * @return 登录结果
     */
    AuthLoginRespVO socialLogin(@Valid AuthSocialLoginReqVO reqVO);


    /**
     * 小程序用户手机号登录
     * @param reqVO
     * @return
     */
    WxXcxAuthLoginRespVO mobileLogin(WxXcxAuthLoginReqVO reqVO);

    /**
     * 小程序用户openid或unionid登录
     * @param reqVO
     * @return
     */
    WxXcxAuthLoginRespVO openidLogin(WxXcxAuthLoginReqVO reqVO);

    /**
     * 企业微信登录
     * @param reqVO
     * @return
     */
    WxXcxAuthLoginRespVO qywxCodeLogin(QywxAuthLoginReqVO reqVO);

    /**
     * 小程序用户手机号登录
     * @param mobile
     * @return
     */
    AuthLoginRespVO mobileLogin(String mobile);


    AuthLoginRespVO createTokenAfterLoginSuccess(Long userId, String username, LoginLogTypeEnum logType);


    AuthLoginRespVO singleSignOn(SingleSignOnReqVO reqVO);

    /**
     * 调训系统单位管理员登录
     * @param reqVO 账号密码
     * @return 登录结果
     */
    AuthLoginRespVO loginForUnit(AuthLoginReqVO reqVO);

    void codeUpdate(CodeAuthUpdateReqVO reqVO);

    /**
     * 根据手机号登录
     * @param reqVO 请求信息
     * @return 返回身份token
     */
    String loginByMobile(@Valid AuthByMobileReqVO reqVO);

    /**
     * 手机验证码登录
     * @param reqVO 登录信息
     * @return 登录结果
     */
    AuthLoginRespVO codeLoginYXUnit(@Valid CodeAuthLoginReqVO reqVO);

    Boolean checkVerificationForYXUnit(String verification,String mobile);
}

package com.unicom.swdx.module.system.service.sms;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.unicom.swdx.framework.captcha.core.util.CaptchaUtils;
import com.unicom.swdx.framework.common.core.KeyValue;
import com.unicom.swdx.framework.common.enums.CommonStatusEnum;
import com.unicom.swdx.framework.common.enums.UserTypeEnum;
import com.unicom.swdx.framework.common.util.validation.ValidationUtils;
import com.unicom.swdx.framework.datapermission.core.annotation.DataPermission;
import com.unicom.swdx.framework.sms.core.client.SmsClient;
import com.unicom.swdx.framework.sms.core.client.SmsClientFactory;
import com.unicom.swdx.framework.sms.core.client.SmsCommonResult;
import com.unicom.swdx.framework.sms.core.client.dto.SmsSendRespDTO;
import com.unicom.swdx.framework.sms.core.property.SmsChannelProperties;
import com.unicom.swdx.module.edu.api.signupunit.SignUpUnitApi;
import com.unicom.swdx.module.system.convert.sms.SmsChannelConvert;
import com.unicom.swdx.module.system.dal.dataobject.sms.SmsChannelDO;
import com.unicom.swdx.module.system.dal.dataobject.sms.SmsTemplateDO;
import com.unicom.swdx.module.system.dal.dataobject.user.AdminUserDO;
import com.unicom.swdx.module.system.dal.mysql.sms.SmsChannelMapper;
import com.unicom.swdx.module.system.dal.mysql.sms.SmsLogMapper;
import com.unicom.swdx.module.system.dal.mysql.sms.SmsTemplateMapper;
import com.unicom.swdx.module.system.dal.mysql.user.AdminUserMapper;
import com.unicom.swdx.module.system.dal.redis.auth.VerificationRedisDAO;
import com.unicom.swdx.module.system.dal.redis.sms.MessageTimesRedisDAO;
import com.unicom.swdx.module.system.dal.redis.sms.MinuteMessageTimesRedisDAO;
import com.unicom.swdx.module.system.enums.sms.SmsMessageEnum;
import com.unicom.swdx.module.system.mq.message.sms.SmsSendMessage;
import com.unicom.swdx.module.system.service.member.MemberService;
import com.unicom.swdx.module.system.service.messagebase.MessageBaseService;
import com.unicom.swdx.module.system.service.user.AdminUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.task.AsyncListenableTaskExecutor;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.module.system.enums.ErrorCodeConstants.*;
import static com.unicom.swdx.module.system.enums.sms.SmsMessageEnum.*;

/**
 * 短信发送 Service 发送的实现
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class SmsSendServiceImpl implements SmsSendService {



    @Resource
    private AdminUserService adminUserService;

    @Resource
    private SmsLogService smsLogService;

    @Resource SmsLogMapper smsLogMapper;

    @Resource
    private VerificationRedisDAO verificationRedisDAO;
    @Resource
    @Lazy
    private SmsSendService self; // 当前类自己的对象

    @Resource
    private MessageTimesRedisDAO timesRedisDAO;

    @Resource
    private MinuteMessageTimesRedisDAO minuteMessageTimesRedisDAO;

    @Resource
    private MessageBaseService messageBaseService;

    @Resource
    private AdminUserMapper userMapper;

    @Value("${unicom.sms.url}")
    private String url;

    @Resource
    private SmsClientFactory smsClientFactory;

    @Resource
    private SignUpUnitApi signUpUnitApi;

    @Resource
    private MemberService memberService;

    @Resource
    private SmsTemplateService smsTemplateService;


    @Override
    @DataPermission(enable = false) // 发送短信时，无需考虑数据权限
    public Long sendSingleSmsToAdmin(String mobile, Long userId, String templateId, Map<String, Object> templateParams) {
        // 如果 mobile 为空，则加载用户编号对应的手机号
        if (StrUtil.isEmpty(mobile)) {
            AdminUserDO user = adminUserService.getUser(userId);
            if (user != null) {
                mobile = user.getMobile();
            }
        }
        // 执行发送
        return this.sendSingleSms(mobile, userId,UserTypeEnum.ADMIN.getValue(), templateId, templateParams);
    }



    @Override
    @DataPermission(enable = false) // 发送短信时，无需考虑数据权限
    public void sendSingleSmsToAdminDX(String mobile, Long userId, String msg) {
        // 如果 mobile 为空，则加载用户编号对应的手机号
        if (StrUtil.isEmpty(mobile)) {
            AdminUserDO user = adminUserService.getUser(userId);
            if (user != null) {
                mobile = user.getMobile();
            }
        }
        // 执行发送
        this.sendSingleSmsApp(mobile, msg);
    }

    /**
     * 发送手机短信
     * @param mobile 手机号
     * @param userId 用户编号(非必填)
     * @param templateCode 短信模板id
     * @param templateParams 短信模板参数
     * @return 短信日志id
     */
    @Override
    public Long sendSingleSms(String mobile, Long userId, Integer userType,
                              String templateCode, Map<String, Object> templateParams) {

        // 限制手机号发送次数
        limitMobileMsgSendTimes(mobile);
//        Map<String, Object> body = SmsUtils.getSendBody(mobile, templateId, templateParams);
//        Long smsLogId = smsLogService.createSmsLog(mobile, userId, templateId, templateParams);
//        self.sendSms(body,smsLogId);
//        // 记录短信日志


        // 校验短信模板是否合法
        SmsTemplateDO template = this.checkSmsTemplateValid(templateCode);
        // 校验短信渠道是否合法
        SmsChannelDO smsChannel = this.checkSmsChannelValid(template.getChannelId());

        // 校验手机号码是否存在
        mobile = this.checkMobile(mobile);
        // 构建有序的模板参数。为什么放在这个位置，是提前保证模板参数的正确性，而不是到了插入发送日志
        List<KeyValue<String, Object>> newTemplateParams = this.buildTemplateParams(template, templateParams);

        // 创建发送日志。如果模板被禁用，则不发送短信，只记录日志
        Boolean isSend = CommonStatusEnum.ENABLE.getStatus().equals(template.getStatus())
                && CommonStatusEnum.ENABLE.getStatus().equals(smsChannel.getStatus());
        ;
        String content =StrUtil.format(template.getContent(), templateParams);
        Long sendLogId = smsLogService.createSmsLog(mobile, userId, template.getApiTemplateId() , templateParams);

        // 发送 MQ 消息，异步执行发送短信
        if (isSend) {


            SmsSendMessage message = new SmsSendMessage().setLogId(sendLogId).setMobile(mobile);
            message.setChannelId(template.getChannelId()).setApiTemplateId(template.getCode()).setTemplateParams(newTemplateParams);
            SmsCommonResult<SmsSendRespDTO>  result  = doSendSmss(message);


        }
        return sendLogId;

    }



    @Override
    public SmsCommonResult<SmsSendRespDTO> sendSingleSmss(String mobile, Long userId, Integer userType,
                              String templateCode, Map<String, Object> templateParams) {

        // 限制手机号发送次数
        limitMobileMsgSendTimes(mobile);
//        Map<String, Object> body = SmsUtils.getSendBody(mobile, templateId, templateParams);
//        Long smsLogId = smsLogService.createSmsLog(mobile, userId, templateId, templateParams);
//        self.sendSms(body,smsLogId);
//        // 记录短信日志


        // 校验短信模板是否合法
        SmsTemplateDO template = this.checkSmsTemplateValid(templateCode);
        // 校验短信渠道是否合法
        SmsChannelDO smsChannel = this.checkSmsChannelValid(template.getChannelId());

        // 校验手机号码是否存在
        mobile = this.checkMobile(mobile);
        // 构建有序的模板参数。为什么放在这个位置，是提前保证模板参数的正确性，而不是到了插入发送日志
        List<KeyValue<String, Object>> newTemplateParams = this.buildTemplateParams(template, templateParams);

        // 创建发送日志。如果模板被禁用，则不发送短信，只记录日志
        Boolean isSend = CommonStatusEnum.ENABLE.getStatus().equals(template.getStatus())
                && CommonStatusEnum.ENABLE.getStatus().equals(smsChannel.getStatus());
        ;
        String content =StrUtil.format(template.getContent(), templateParams);
        Long sendLogId = smsLogService.createSmsLog(mobile, userId, template.getApiTemplateId() , templateParams);

        // 发送 MQ 消息，异步执行发送短信
        SmsSendMessage message = new SmsSendMessage().setLogId(sendLogId).setMobile(mobile);
        message.setChannelId(template.getChannelId()).setApiTemplateId(template.getCode()).setTemplateParams(newTemplateParams);
        SmsCommonResult<SmsSendRespDTO>  result  = doSendSmss(message);
        SmsSendRespDTO smsSendRespDTO = new SmsSendRespDTO();
        smsSendRespDTO.setSerialNo(String.valueOf(sendLogId));
        result.setData(smsSendRespDTO);
        return result;

    }

    /**
     * 发送手机短信
     * @param mobile 手机号
     * @return 短信日志id
     */
    public void sendSingleSmsApp(String mobile, String msg) {
        this.checkMobile(mobile);
        // 限制手机号发送次数
        limitMobileMsgSendTimes(mobile);
        messageBaseService.sendSingleMessage(mobile,msg);
    }


    /**
     * 校验并限制发送手机短信次数
     * @param mobile 手机号
     */
    private void limitMobileMsgSendTimes(String mobile) {
        //限制一个手机号1天最多只能发送10次短信
        long time = timesRedisDAO.get(mobile);
        if (time >= 10000) {
            throw exception(MASSAGE_TIMES_OVER_10);
        }
        timesRedisDAO.set(mobile);
    }

    @Async
    @Override
    public void sendSms(Map<String, Object> body,Long smsLogId) {
        HttpResponse response = null;
        boolean sendStatus = false;
        try {
            response = HttpUtil.createPost(url)
                    .body(JSON.toJSONString(body), "application/json").execute();
        } catch (Exception e) {
            log.error("短信发送失败，{}", body);
        }
        JSONObject resp = JSONObject.parseObject(response.body());
        if (!resp.get("respcode").equals(3000)) {
            log.error("短信发送失败，{}", resp);
        } else {
            sendStatus = true;
            log.info("短信发送成功，{}",resp);
        }
        smsLogService.updateSmsLog(smsLogId, sendStatus, response.body());
    }


    public static String replaceParams(String template, List<KeyValue<String, Object>>  map) {
        // 遍历Map中的键值对，将模板中的参数替换为对应的值
        for (int i = 0; i < map.size(); i++) {
            String key = map.get(i).getKey();
            String value = map.get(i).getValue().toString();
            template = template.replace("{" + key + "}", value);
        }
        return template;
    }


    @Override
    public void doSendSms(SmsSendMessage message) {
        // 获得渠道对应的 SmsClient 客户端
        SmsClient smsClient = smsClientFactory.getSmsClient(message.getChannelId());
        Assert.notNull(smsClient, "短信客户端({}) 不存在", message.getChannelId());
        // 发送短信
        SmsTemplateDO template = this.checkSmsTemplateValid(message.getApiTemplateId());

        SmsCommonResult<SmsSendRespDTO> sendResult = smsClient.sendSms(message.getLogId(), message.getMobile(),
                replaceParams(template.getContent() , message.getTemplateParams() ), message.getTemplateParams());
        smsLogService.updateSmsSendResult(message.getLogId(), sendResult.getCode(), sendResult.getMsg(),
                sendResult.getApiCode(), sendResult.getApiMsg(), sendResult.getApiRequestId(),
                sendResult.getData() != null ? sendResult.getData().getSerialNo() : null);


    }

    @Override
    public SmsCommonResult<SmsSendRespDTO> doSendSmss(SmsSendMessage message) {
        // 获得渠道对应的 SmsClient 客户端
        SmsClient smsClient = smsClientFactory.getSmsClient(message.getChannelId());
        Assert.notNull(smsClient, "短信客户端({}) 不存在", message.getChannelId());
        // 发送短信
        SmsTemplateDO template = this.checkSmsTemplateValid(message.getApiTemplateId());

        SmsCommonResult<SmsSendRespDTO> sendResult = smsClient.sendSms(message.getLogId(), message.getMobile(),
                replaceParams(template.getContent() , message.getTemplateParams() ), message.getTemplateParams());
        smsLogService.updateSmsSendResult(message.getLogId(), sendResult.getCode(), sendResult.getMsg(),
                sendResult.getApiCode(), sendResult.getApiMsg(), sendResult.getApiRequestId(),
                sendResult.getData() != null ? sendResult.getData().getSerialNo() : null);


        return sendResult;
    }

    public static  Cache<String, List<SmsTemplateDO>> resultCache=
            CacheBuilder.newBuilder()
                    .initialCapacity(1) // 初始容量
                    .maximumSize(1)   // 设定最大容量
                    .expireAfterWrite(10, TimeUnit.MINUTES) // 设定写入过期时间
                    .concurrencyLevel(8)  // 设置最大并发写操作线程数
                    .build();


    public static  Cache<String, List<SmsChannelDO>> resultCachesmchannel=
            CacheBuilder.newBuilder()
                    .initialCapacity(1) // 初始容量
                    .maximumSize(1)   // 设定最大容量
                    .expireAfterWrite(10, TimeUnit.MINUTES) // 设定写入过期时间
                    .concurrencyLevel(8)  // 设置最大并发写操作线程数
                    .build();

    @Resource
    private SmsTemplateMapper smsTemplateMapper;

    @Resource
    private SmsChannelMapper smsChannelMapper;




    @VisibleForTesting
    public SmsTemplateDO checkSmsTemplateValid(String templateCode) {
        // 获得短信模板。考虑到效率，从缓存中获取
        List<SmsTemplateDO> templateDOList  = null;
        SmsTemplateDO  template = null;
        try {
            templateDOList = resultCache.get("templateCode" ,() -> smsTemplateMapper.selectList());
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }

        for (int i = 0; i < templateDOList.size(); i++) {
            SmsTemplateDO  it = templateDOList.get(i);
            if(it.getCode()!=null &&  it.getCode().trim().equals(templateCode)){
                template = it;
            }
        }

        // 短信模板不存在
        if (template == null) {
            throw exception(SMS_SEND_TEMPLATE_NOT_EXISTS);
        }

        return template;
    }


    @VisibleForTesting
    public SmsChannelDO checkSmsChannelValid(Long channelId) {
        // 获得短信模板。考虑到效率，从缓存中获取
        List<SmsChannelDO> templateDOList  = null;
        SmsChannelDO  template = null;
        try {
            templateDOList = resultCachesmchannel.get("templatechannel" ,() -> {
                List<SmsChannelDO> smsChannels = smsChannelMapper.selectList();
                List<SmsChannelProperties> propertiesList = SmsChannelConvert.INSTANCE.convertList02(smsChannels);
                propertiesList.forEach(properties -> smsClientFactory.createOrUpdateSmsClient(properties));
                return smsChannels;
            });
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }

        for (int i = 0; i < templateDOList.size(); i++) {
            SmsChannelDO  it = templateDOList.get(i);
            if(it.getId()!=null &&  it.getId().equals(channelId)){
                template = it;
            }
        }

        // 短信模板不存在
        if (template == null) {
            throw exception(SMS_SEND_TEMPLATE_NOT_EXISTS);
        }

        return template;
    }


    /**
     * 将参数模板，处理成有序的 KeyValue 数组
     * <p>
     * 原因是，部分短信平台并不是使用 key 作为参数，而是数组下标，例如说腾讯云 https://cloud.tencent.com/document/product/382/39023
     *
     * @param template       短信模板
     * @param templateParams 原始参数
     * @return 处理后的参数
     */
    @VisibleForTesting
    public List<KeyValue<String, Object>> buildTemplateParams(SmsTemplateDO template, Map<String, Object> templateParams) {
        return template.getParams().stream().map(key -> {
            Object value = templateParams.get(key);
            if (value == null) {
                throw exception(SMS_SEND_MOBILE_TEMPLATE_PARAM_MISS, key);
            }
            return new KeyValue<>(key, value);
        }).collect(Collectors.toList());
    }

    @VisibleForTesting
    public String checkMobile(String mobile) {
        if (StrUtil.isBlank(mobile)) {
            throw exception(SMS_SEND_MOBILE_NOT_EXISTS);
        }
        return mobile;
    }

    @Override
    public void receiveSmsStatus(String channelCode, String text) throws Throwable {
        // 获得渠道对应的 SmsClient 客户端
//        SmsClient smsClient = smsClientFactory.getSmsClient(channelCode);
//        Assert.notNull(smsClient, "短信客户端({}) 不存在", channelCode);
//        // 解析内容
//        List<SmsReceiveRespDTO> receiveResults = smsClient.parseSmsReceiveStatus(text);
//        if (CollUtil.isEmpty(receiveResults)) {
//            return;
//        }
//        // 更新短信日志的接收结果. 因为量一般不大，所以先使用 for 循环更新
//        receiveResults.forEach(result -> smsLogService.updateSmsReceiveResult(result.getLogId(),
//                result.getSuccess(), result.getReceiveTime(), result.getErrorCode(), result.getErrorMsg()));
    }

    /**
     * 发送手机验证码
     * @param mobile 手机号
     * @param verificationTypeEnum 验证码类型枚举
     * @return
     */
    @Override
    public void getVerification(String mobile, SmsMessageEnum verificationTypeEnum) {
        if (!ValidationUtils.isMobile(mobile)) {
            throw exception(MOBILE_ILLEGAL);
        }
        // 如果是重置密码或者系统登录需要先判断手机号是否存在
        if ((Objects.equals(RESET_PASSWORD,verificationTypeEnum) || (Objects.equals(LOGIN,verificationTypeEnum)))  && Objects.isNull(adminUserService.getUserByMobile(mobile))) {
            throw exception(AUTH_MOBILE_NOT_EXISTS);
        }

        List<String> phoneList = signUpUnitApi.getPhoneList().getCheckedData();
        if (phoneList == null || !phoneList.contains(mobile)) {
            throw exception(SIGN_UP_UNIT_NOT_BIND);
        }
        // 限制同一手机号只能1分钟发送一条验证码短信
        if (!minuteMessageTimesRedisDAO.set(mobile)) {
            throw exception(MASSAGE_TIMES_OVER_1);
        }
        String verification = CaptchaUtils.getNumCaptcha();
        verificationRedisDAO.set(verificationTypeEnum,mobile,verification);
        String msgContent = verificationTypeEnum.getMsgContent();
        String content = StrUtil.replace(msgContent, "{}", verification);
        self.sendSingleSmsToAdminDX(mobile,null,content);
    }

    @Override
    public void getVerificationYXUnit(String mobile, SmsMessageEnum verificationTypeEnum) {
        if (!ValidationUtils.isMobile(mobile)) {
            throw exception(MOBILE_ILLEGAL);
        }
        // 如果是重置密码或者系统登录需要先判断手机号是否存在
        if ((Objects.equals(RESET_PASSWORD,verificationTypeEnum) || (Objects.equals(LOGIN,verificationTypeEnum)))  && Objects.isNull(adminUserService.getUserByMobile(mobile))) {
            throw exception(AUTH_MOBILE_NOT_EXISTS);
        }

        // 限制同一手机号只能1分钟发送一条验证码短信
        if (!minuteMessageTimesRedisDAO.set(mobile)) {
            throw exception(MASSAGE_TIMES_OVER_1);
        }
        String verification = CaptchaUtils.getNumCaptcha();
        verificationRedisDAO.set(verificationTypeEnum,mobile,verification);
        String msgContent = verificationTypeEnum.getMsgContent();
        String content = StrUtil.replace(msgContent, "{}", verification);
        self.sendSingleSmsToAdminDX(mobile,null,content);
    }

}

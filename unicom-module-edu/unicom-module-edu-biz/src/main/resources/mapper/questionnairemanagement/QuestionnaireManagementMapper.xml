<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.swdx.module.edu.dal.mysql.questionnairemanagement.QuestionnaireManagementMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <sql id="selectQuestionnaireManagementRespVO">
        SELECT
        pqm.id id,
        pqm.title title,
        pqm.is_default isDefault,
        pqm.status status,
        pqm.create_time createTime,
        pqm.built_in builtIn,
        pqm.is_template isTemplate,
        pqm.template_type templateType,
        pqm.creator creator
        FROM
        pg_questionnaire_management pqm

        <where>
            pqm.deleted = 0
            <if test="reqVO.title != null and reqVO.title != ''">
                AND pqm.title like CONCAT('%', #{reqVO.title}, '%')
            </if>
            <if test="reqVO.status != null and reqVO.status != ''">
                AND pqm.status = #{reqVO.status}
            </if>
            <if test="reqVO.builtIn !=null">
                AND pqm.built_in = #{reqVO.builtIn}
            </if>
            <if test="reqVO.startTime != null and reqVO.endTime != null" >
                AND pqm.create_time between #{reqVO.startTime} and #{reqVO.endTime}
            </if>
            AND NOT (
            is_template = 1
            AND template_type = 0
            AND creator &lt;&gt; #{loginUserId}
            )
        </where>
        ORDER BY
            pqm.update_time DESC,
            pqm.built_in DESC,
        <if test="reqVO.sortField == null or reqVO.sortField == 0">
            pqm.id
        </if>
        <if test="reqVO.sortField != null and reqVO.sortField == 1">
            pqm.status
        </if>
        <if test="reqVO.isDesc != null and reqVO.isDesc == false">
            ASC
        </if>
        <if test="reqVO.isDesc == null or reqVO.isDesc == true">
            DESC
        </if>

    </sql>


    <select id="selectPageByPageVO"
            resultType="com.unicom.swdx.module.edu.controller.admin.questionnairemanagement.vo.QuestionnaireManagementRespVO">
        <include refid="selectQuestionnaireManagementRespVO"/>
    </select>
    <select id="countDefault" resultType="java.lang.Integer">
        select count(*) from pg_questionnaire_management where is_default = '1' and status = 1 and deleted = 0 and tenant_id = #{tenantId}
    </select>
    <select id="getByEducateForm" parameterType="java.lang.Long" resultType="java.lang.Long">
        select id from pg_questionnaire_management where topic_educate_form like CONCAT('%', #{educateFormId}, '%') and tenant_id = #{tenantId} and status = '1'
    </select>
    <select id="getDefaultQuestionnaire" resultType="java.lang.Long">
        select id from pg_questionnaire_management where is_default = '1' and status = '1' and deleted = false and tenant_id = #{tenantId} limit 1
    </select>
    <select id="countByEducateForm" resultType="java.lang.Long" parameterType="java.lang.String">
        select count(*) from pg_questionnaire_management where topic_educate_form like CONCAT('%', #{educateForm}, '%') and status = '1' and deleted = 0
    </select>
    <select id="getCollectingEducateForm" resultType="java.lang.String" parameterType="java.lang.Long">
        select topic_educate_form from pg_questionnaire_management where status = '1' and deleted = false and tenant_id = #{tenantId}
    </select>
    <select id="countBuiltId" resultType="java.lang.Long">
        select count(*) from pg_questionnaire_management where built_in = true and deleted = false
    </select>
    <select id="getTemplate"
            resultType="com.unicom.swdx.module.edu.controller.admin.questionnairemanagement.vo.QuestionnaireManagementRespVO">
        select *
        from pg_questionnaire_management
        where is_template = true
          and deleted = false
          <if test="templateType == 1">
            and template_type = #{templateType}
          </if>
          <if test="templateType == 0">
            and creator = #{loginUserId}
            and template_type = #{templateType}
          </if>
    </select>
    <select id="selectStatPageByPageVO"
            resultType="com.unicom.swdx.module.edu.controller.admin.questionnairemanagement.vo.QuestionnaireStatsRespVO">
        SELECT
        pqm.id id,
        pqm.title title,
        pqm.status status,
        pqm.publish_time publishTime,
        pqm.publisher publisher,
        pqm.publish_scale publishScale
        FROM
        pg_questionnaire_management pqm

        <where>
            pqm.deleted = 0
            and is_template = false
            <if test="reqVO.title != null and reqVO.title != ''">
                AND pqm.title like CONCAT('%', #{reqVO.title}, '%')
            </if>
            <if test="reqVO.status != null and reqVO.status != ''">
                AND pqm.status = #{reqVO.status}
            </if>
            <if test="reqVO.startTime != null and reqVO.endTime != null" >
                AND  pqm.publish_time between #{reqVO.startTime} and #{reqVO.endTime}
            </if>
        </where>
        ORDER BY
            pqm.publish_time DESC
        <if test="reqVO.sortField == null or reqVO.sortField == 0">
            ,pqm.id
        </if>
        <if test="reqVO.sortField != null and reqVO.sortField == 1">
            ,pqm.status
        </if>
        <if test="reqVO.isDesc != null and reqVO.isDesc == false">
            ASC
        </if>
        <if test="reqVO.isDesc == null or reqVO.isDesc == true">
            DESC
        </if>
    </select>
    <select id="selectEvaluationCount"
            resultType="com.unicom.swdx.module.edu.controller.admin.questionnairemanagement.vo.EvaluationCountRespVO">
        select et.name, per.handle as status from pg_evaluation_response per
        left join edu_trainee et on et.id = per.student_id
        where per.deleted = false
          and per.handle = #{reqVO.status}
          and per.publish_scale = #{reqVO.classId}
          and per.questionnaire_id = #{reqVO.questionnaireId}
        <if test="reqVO.name != null and reqVO.name != ''">
            and et.name like CONCAT('%',#{reqVO.name},'%')
        </if>
    </select>
    <select id="selectDetail"
            resultType="com.unicom.swdx.module.edu.controller.admin.questionnairemanagement.vo.DetailVO">
        select distinct ped.question_id,
               ped.content,
               ped.option_id,
               ped.student_id,
               pqm.title,
               pqm.publish_time,
               ped.question_type,
               et.name studentName,
               ec.class_name className,
               ped.update_time update_time,
               pqma.serial_number,
               per.submit_time as submitTime
               from pg_evaluation_detail ped
            left join pg_questionnaire_management pqm on ped.questionnaire_id = pqm.id
            left join pg_question_management pqma on ped.question_id = pqma.id
            left join pg_evaluation_response per on (per.id = ped.response_id)
            left join edu_trainee et on ped.student_id = et.id
            left join edu_class_management ec on ped.publish_scale = ec.id
        where ped.deleted = false
          and per.handle = 1
          and ped.questionnaire_id = #{reqVO.questionnaireId}
          and ped.publish_scale = #{reqVO.classId}
        <if test="reqVO.name != null and reqVO.name != ''">
            and et.name like CONCAT('%',#{reqVO.name},'%')
        </if>
        order by ped.update_time desc, pqma.serial_number ASC
    </select>
</mapper>

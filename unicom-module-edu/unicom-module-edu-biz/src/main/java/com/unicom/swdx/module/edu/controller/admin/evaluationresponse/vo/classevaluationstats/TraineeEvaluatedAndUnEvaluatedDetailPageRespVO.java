package com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.classevaluationstats;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.unicom.swdx.framework.jackson.core.databind.LocalDateTimePatternSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Description: 班次评估统计-学员已评、未评详情分页 Response VO
 * @date 2024-11-19
 */
@ApiModel("班次评估统计分页-学员已评、未评详情分页 Response VO")
@Data
public class TraineeEvaluatedAndUnEvaluatedDetailPageRespVO {

    @ApiModelProperty(value = "序号", example = "1")
    private Long serialNumber;

    @ApiModelProperty(value = "排课ID", example = "1")
    private Long classCourseId;

    @ApiModelProperty(value = "课程id", example = "1")
    private Long courseId;

    @ApiModelProperty(value = "课程名称", example = "课程")
    private String courseName;

    @ApiModelProperty(value = "授课教师id", example = "1")
    private Long teacherId;

    @ApiModelProperty(value = "授课教师", example = "李四")
    private String teacherName;

    @ApiModelProperty(value = "授课时间", example = "2021-01-01 上午 00:00 至 00:00")
    private String classDuration;

    @ApiModelProperty(value = "授课开始时间", example = "2021-01-01 00:00:00")
    @JsonSerialize(using = LocalDateTimePatternSerializer.class)
    private LocalDateTime classStartTime;

    @ApiModelProperty(value = "授课结束时间", example = "2021-01-01 00:00:00")
    @JsonSerialize(using = LocalDateTimePatternSerializer.class)
    private LocalDateTime classEndTime;

    @ApiModelProperty(value = "授课日期", example = "2021-01-01")
    private String classDate;

    @ApiModelProperty(value = "午别时间 0-上午 1-下午 2-晚上", example = "1")
    private Integer dayPeriod;

}

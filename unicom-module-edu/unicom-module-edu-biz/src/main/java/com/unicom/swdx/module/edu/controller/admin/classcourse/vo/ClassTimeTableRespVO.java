package com.unicom.swdx.module.edu.controller.admin.classcourse.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.ClassInfoRespVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Data
public class ClassTimeTableRespVO {

    private Long id;

    @ApiModelProperty(value = "班级id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long classId;

    @ApiModelProperty(value = "班级名称")
    private String className;

    //当天的日期 格式为：yyyy-MM-dd
    private String date;

    private Boolean holiday;
    private List<DateCourseVO> dateCourse = new ArrayList<>();


    @Data
    public static class DateCourseVO{
        private String type;
        private List<CourseVO> timeCourse = new ArrayList<>();
    }

    @Data
    public static class CourseVO {
        private Long id;

        private String courseType;
        //09:30-10:30
        private String time;

        //教师id
        private String teachers ;

        private String courseTitle;
        private String coach;

        private Long courseId;

        @ApiModelProperty(value = "是否到课考勤，true-开，false-关")
        private boolean openCheck;

        @ApiModelProperty(value = "教室")
        private String classroom;

        @ApiModelProperty(value = "授课教师")
        private String teacherName;

        @ApiModelProperty(value = "班级id")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long classId;

        @ApiModelProperty(value = "班级名称")
        private String className;

        @ApiModelProperty(value = "是否党政领导讲课，0不是，1是")
        private Boolean isLeaderLecture;

        @ApiModelProperty(value = "是否厅级领导授课")
        private Boolean isDepartmentLeader;

        @ApiModelProperty(value = "开始时间")
        private String beginTime;

        @ApiModelProperty(value = "结束时间")
        private String endTime;

        @ApiModelProperty(value = "是否合班授课")
        private Boolean isMerge;

        @ApiModelProperty(value = "是否显示合班授课标签 true - 是，false - 否")
        private Boolean isShowMergeTag;

        @ApiModelProperty(value = "合班授课班级列表")
        private List<ClassInfoRespVO> mergedClass;

        @ApiModelProperty(value = "是否调课")
        private Boolean isChange;
    }
}

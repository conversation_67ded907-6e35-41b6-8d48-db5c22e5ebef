package com.unicom.swdx.module.edu.controller.admin.questionnairemanagement.vo;

import com.unicom.swdx.module.edu.controller.admin.questionlogic.vo.QuestionLogicRespVO;
import com.unicom.swdx.module.edu.controller.admin.questionlogic.vo.QuestionLogicSaveReqVO;
import com.unicom.swdx.module.edu.controller.admin.questionmanagement.vo.QuestionManagementRespVO;
import com.unicom.swdx.module.edu.controller.admin.questionnairedetail.vo.QuestionnaireDetailRespVO;
import com.unicom.swdx.module.edu.controller.admin.questionnairedetail.vo.QuestionnaireDetailSaveReqVO;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 评估问卷管理 Response VO")
@Data
@ExcelIgnoreUnannotated
public class QuestionnaireManagementRespVO {

    @Schema(description = "主键ID",  example = "5655")
    @ExcelProperty(value = "主键ID")
    private Long id;

    @Schema(description = "问卷标题")
    @ExcelProperty(value = "问卷标题", index = 1)
    private String title;

    @Schema(description = "问卷副标题")
    @ExcelProperty("问卷副标题")
    private String subtitle;

    @Schema(description = "是否默认问卷 (0: 否, 1: 是)")
    @ExcelProperty("是否默认问卷 (0: 否, 1: 是)")
    private String isDefault;

    @Schema(description = "状态 0未发布1已发布 2 已结束", example = "1")
    @ExcelProperty("状态 0未发布1已发布 2 已结束")
    private String status;

    @Schema(description = "创建部门")
    @ExcelProperty("创建部门")
    private Long createDept;

    @Schema(description = "创建时间")
    @ExcelProperty(value = "创建时间", index = 3)
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    @ExcelProperty(value = "创建人")
    private Long creator;

    @Schema(description = "创建人")
    @ExcelProperty(value = "创建人", index = 4)
    private String creatorName;

    @Schema(description = "备注", example = "你猜")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "专题库教学形式")
    @ExcelProperty("专题库教学形式")
    private String topicEducateForm;

    @Schema(description = "最低分")
    @ExcelProperty("最低分")
    private Integer lowscore;

    @Schema(description = "启用最低分")
    @ExcelProperty("启用最低分")
    private Boolean lowscoreTag;

    @Schema(description = "最低字数")
    @ExcelProperty("最低字数")
    private Integer lowword;

    @Schema(description = "启用最低字数")
    @ExcelProperty("启用最低字数")
    private Boolean lowwordTag;

    @Schema(description = "启用时效限制")
    @ExcelProperty("启用时效限制")
    private Boolean timeTag;

    @Schema(description = "失效天数")
    @ExcelProperty("失效天数")
    private Integer timeLimit;

    @Schema(description = "评估项列表")
    private List<QuestionManagementRespVO> questions;

    @ApiModelProperty(value = "序号")
    @ExcelProperty(value = "序号",index = 0)
    private Long serialNumber;

    @Schema(description = "是否内置", example = "false")
    private Boolean builtIn;

    @Schema(description = "是否模板", example = "false")
    @ExcelProperty(value = "是否模板", index = 2)
    private Boolean isTemplate;

    @Schema(description = "模板类型，0我的，1系统", example = "0")
    private Integer templateType;

    /**
     * 最后发布时间
     */
    @Schema(description = "最后发布时间", example = "0")
    private LocalDateTime publishTime;

    @Schema(description = "发布人", example = "0")
    private Long publisher;

    @Schema(description = "发布人", example = "0")
    private String publisherName;

    @Schema(description = "发布范围", example = "0")
    private Long publisherScale;
}

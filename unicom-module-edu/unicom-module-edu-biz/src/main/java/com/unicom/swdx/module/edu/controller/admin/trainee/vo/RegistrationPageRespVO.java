package com.unicom.swdx.module.edu.controller.admin.trainee.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

/**
 * @ClassName: RegistrationPageReqVO
 * @Author: lty
 * @Date: 2024/10/9 14:32
 */
@Data
@ApiModel(value = "报名详情分页返回VO")
public class RegistrationPageRespVO {
    @ApiModelProperty(value = "班级id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty(value = "班级编码")
    @ExcelProperty(value = "班级编码")
    private String classNameCode;

    @ApiModelProperty(value = "班级名称")
    @ExcelProperty(value = "班级名称")
    private String className;

    @ApiModelProperty(value = "开班时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty(value = "开班时间")
    private LocalDate classOpenTime;

    @ApiModelProperty(value = "结业时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty(value = "结业时间")
    private LocalDate completionTime;

    @ApiModelProperty(value = "校区")
    @ExcelProperty(value = "校区")
    private String campus;

    @ApiModelProperty(value = "预计报名人数")
    @ExcelProperty(value = "预计报名人数")
    private Integer peopleNumber;

    @ApiModelProperty(value = "实际报名人数")
    @ExcelProperty(value = "实际报名人数")
    private Long actualPeopleNumber;

    @ApiModelProperty(value = "已报到人数")
    @ExcelProperty(value = "已报到人数")
    private Long reportNumber;

    private Integer classStatus;

    @ExcelProperty(value = "班级属性")
    @ExcelIgnore
    private Integer classAttribute;
}

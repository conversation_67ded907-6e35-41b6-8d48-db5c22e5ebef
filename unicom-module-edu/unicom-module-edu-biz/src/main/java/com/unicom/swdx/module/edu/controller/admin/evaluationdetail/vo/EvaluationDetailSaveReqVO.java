package com.unicom.swdx.module.edu.controller.admin.evaluationdetail.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 评估详情新增/修改 Request VO")
@Data
public class EvaluationDetailSaveReqVO {

    @Schema(description = "主键", example = "18792")
    private Long id;

    @Schema(description = "问卷id", example = "9284")
    private Long questionnaireId;

    @Schema(description = "问题id", example = "13794")
    private Long questionId;

    @Schema(description = "问题id", example = "13794")
    private Long responseId;

    @Schema(description = "问题类型1打分2单选3简答", example = "2")
    private String questionType;

    @Schema(description = "评卷人id", example = "14762")
    private Long studentId;

    @Schema(description = "打分题得分")
    private Long score;

    @Schema(description = "选择题选项", example = "21307")
    private String optionId;

    @Schema(description = "租户id", example = "21307")
    private Long tenantId;

    @Schema(description = "简单题内容")
    private String content;

    @Schema(description = "课程id", example = "24082")
    private Long classCourseId;

    private Boolean handle;

    @Schema(description = "发布范围", example = "23817")
    private Long publishScale;


}
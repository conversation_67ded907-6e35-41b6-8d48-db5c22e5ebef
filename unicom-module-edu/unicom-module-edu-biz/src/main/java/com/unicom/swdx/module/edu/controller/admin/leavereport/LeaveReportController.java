package com.unicom.swdx.module.edu.controller.admin.leavereport;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.util.object.BeanUtils;
import com.unicom.swdx.framework.excel.core.util.ExcelUtils;
import com.unicom.swdx.module.edu.controller.admin.leavereport.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.leavereport.LeaveReportDO;
import com.unicom.swdx.module.edu.dal.dataobject.leavereport.LeaveReportDetailDO;
import com.unicom.swdx.module.edu.service.leavereport.LeaveReportService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 班主任离校报备管理")
@RestController
@RequestMapping("/edu/leave-report")
@Validated
public class LeaveReportController {

    @Resource
    private LeaveReportService leaveReportService;

    @PostMapping("/create")
    @Operation(summary = "创建离校申请")
    @PreAuthorize("@ss.hasPermission('edu:question-category-management:create')")
    public CommonResult<Long> createLeaveReport(@Valid @RequestBody LeaveReportCreateReqVO createReqVO) {
        return success(leaveReportService.createLeaveReport(createReqVO));
    }

//    @PostMapping("/update")
//    @Operation(summary = "更新离校申请")
//    @PreAuthorize("@ss.hasPermission('edu:question-category-management:update')")
//    public CommonResult<Boolean> updateLeaveReport(@Valid @RequestBody LeaveReportSaveReqVO updateReqVO) {
//        LeaveReportService.updateLeaveReport(updateReqVO);
//        return success(true);
//    }

//    @PostMapping("/delete")
//    @Operation(summary = "删除离校申请")
//    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('edu:question-category-management:delete')")
//    public CommonResult<Boolean> deleteLeaveReport(@RequestParam("id") Long id) {
//        leaveReportService.deleteLeaveReport(id);
//        return success(true);
//    }

    @GetMapping("/getDetail")
    @Operation(summary = "获得离校申请详情")
    @Parameter(name = "leaveId", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('edu:question-category-management:query')")
    public CommonResult<TeacherLeaveReportLeaveVO> getLeaveReport(@RequestParam("leaveId") Long leaveId, @RequestParam(value = "fillStatus",required = false) Integer fillStatus) {
        return success(leaveReportService.getLeaveReportDetail(leaveId, fillStatus));
    }

    @GetMapping("/getTime")
    @Operation(summary = "获得离校报备起止时间")
    @Parameter(name = "leaveId", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('edu:question-category-management:query')")
    public CommonResult<LeaveReportTimeVO> getLeaveReportTime(@RequestParam("leaveId") Long leaveId) {
        return success(leaveReportService.getLeaveReportTime(leaveId));
    }

//    @GetMapping("/remind")
//    @Operation(summary = "班主任一键提醒")
//    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('edu:question-category-management:query')")
//    public CommonResult<LeaveReportRespVO> remind(@RequestParam("id") Long id) {
//        LeaveReportDO LeaveReport = leaveReportService.getLeaveReport(id);
//        return success(BeanUtils.toBean(LeaveReport, LeaveReportRespVO.class));
//    }

    @GetMapping("/getByClassId")
    @Operation(summary = "获得班级已发起的离校申请")
    @PreAuthorize("@ss.hasPermission('edu:question-category-management:query')")
    public CommonResult<List<LeaveReportRespVO>> getLeaveReportList(@RequestParam("classId") Long classId) {
        return success(leaveReportService.getLeaveReportList(classId));
    }

    @PostMapping("/remind")
    @Operation(summary = "一键提醒")
    @PreAuthorize("@ss.hasPermission('edu:question-category-management:create')")
    public CommonResult<Boolean> remind(@Valid @RequestBody LeaveReportRemindReqVO remindReqVO) {
        return success(leaveReportService.remind(remindReqVO));
    }

//    @GetMapping("/export-excel")
//    @Operation(summary = "导出题目类别管理 Excel")
//    @PreAuthorize("@ss.hasPermission('edu:question-category-management:export')")
//
//    public void exportLeaveReportExcel(@Valid LeaveReportListReqVO listReqVO,
//                                                      HttpServletResponse response) throws IOException {
//        List<LeaveReportDO> list = leaveReportService.getLeaveReportList(listReqVO);
//        // 导出 Excel
//        ExcelUtils.write(response, "题目类别管理.xls", "数据", LeaveReportRespVO.class,
//                BeanUtils.toBean(list, LeaveReportRespVO.class));
//    }
}

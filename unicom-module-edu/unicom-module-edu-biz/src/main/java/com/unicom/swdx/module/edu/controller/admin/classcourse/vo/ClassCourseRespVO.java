package com.unicom.swdx.module.edu.controller.admin.classcourse.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import io.swagger.annotations.*;

@ApiModel("管理后台 - 班级课程安排 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ClassCourseRespVO extends ClassCourseBaseVO {

    @ApiModelProperty(value = "唯一标识", required = true)
    private Long id;

    @ApiModelProperty(value = "授课者名称（教师或部门）字符串，多位教师之间逗号间隔")
    private String teacherName;

    @ApiModelProperty(value = "授课者Id（教师或部门）字符串，多位教师之间逗号间隔")
    private String teacherIds;

    @ApiModelProperty(value = "教室名称")
    private String classroomName;

    @ApiModelProperty(value = "课程名称")
    private String courseName;

    @ApiModelProperty(value = "课程类型")
    private String courseType;

    /**
     * 教学形式字典ID
     */
    @ApiModelProperty(value = "教学形式字典ID")
    private Long educateFormId;

    /**
     * 教学活动库活动类型字典ID
     */
    @ApiModelProperty(value = "教学活动库活动类型字典ID")
    private String activityType;


    /**
     * 教学形式字典名称
     */
    @ApiModelProperty(value = "教学形式字典名称")
    private String educateFormName;

    /**
     * 课程分类字典ID
     */
    @ApiModelProperty(value = "课程分类字典ID")
    @TableField(exist = false)
    private Long themeId;

    /**
     * 课程分类字典名称
     */
    @ApiModelProperty(value = "课程分类字典名称")
    @TableField(exist = false)
    private String themeName;

    /**
     * 开始时间结束时间数组拼接字符串，方便前端回显
     */
    @ApiModelProperty(value = "开始时间结束时间数组拼接字符串，方便前端回显")
    private String timeRange;

    /**
     * 开始时间结束时间数组，方便前端回显
     */
    @ApiModelProperty(value = "开始时间结束时间数组，方便前端回显")
    private List<LocalDateTime> localDateTimeList;

    /**
     * 常用教室id
     */
    @ApiModelProperty(value = "常用教室id")
    private Long classroomIdAlways;

    /**
     * 常用教室名称
     */
    @ApiModelProperty(value = "常用教室名称")
    private String classroomIdAlwaysName;

    /**
     * 日期date对应周几
     */
    @ApiModelProperty(value = "日期date对应周几")
    private String weekDay;

    /**
     * 课程类型Id
     */
    @ApiModelProperty(value = "课程类型Id")
    private String courseTypeId;

    /**
     * 是否党政领导讲课，0不是，1是
     */
    private Boolean isLeaderLecture;

    @ApiModelProperty(value = "是否显示合班授课标签 true - 是，false - 否")
    private Boolean isShowMergeTag;
}

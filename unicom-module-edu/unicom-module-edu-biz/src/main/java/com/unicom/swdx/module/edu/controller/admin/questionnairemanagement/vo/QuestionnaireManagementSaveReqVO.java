package com.unicom.swdx.module.edu.controller.admin.questionnairemanagement.vo;

import com.unicom.swdx.module.edu.controller.admin.questionlogic.vo.QuestionLogicSaveReqVO;
import com.unicom.swdx.module.edu.controller.admin.questionmanagement.vo.QuestionManagementSaveReqVO;
import com.unicom.swdx.module.edu.controller.admin.questionnairedetail.vo.QuestionnaireDetailSaveReqVO;
import com.unicom.swdx.module.edu.dal.dataobject.classcourse.ClassCourseDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.Valid;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 评估问卷管理新增/修改 Request VO")
@Data
public class QuestionnaireManagementSaveReqVO {

    @Schema(description = "主键ID",  example = "5655")
    private Long id;

    @Schema(description = "问卷标题")
    private String title;

    @Schema(description = "问卷标题")
    private String subtitle;

    @Schema(description = "是否默认问卷 (0: 否, 1: 是)")
    private String isDefault;

    @Schema(description = "状态 0未发布1已发布 2 已结束", example = "1")
    private String status;

    @Schema(description = "创建部门")
    private Long createDept;

    @Schema(description = "创建人")
    private Long creator;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "专题库教学形式")
    private String topicEducateForm;

    @Schema(description = "最低分")
    private Integer lowscore;

    @Schema(description = "启用最低分")
    private Boolean lowscoreTag;

    @Schema(description = "最低字数")
    private Integer lowword;

    @Schema(description = "启用最低字数")
    private Boolean lowwordTag;

    @Schema(description = "启用时效限制")
    private Boolean timeTag;

    @Schema(description = "失效天数")
    private Integer timeLimit;

//    @Schema(description = "评估项列表")
//    private List<QuestionnaireDetailSaveReqVO> questions;

    @Schema(description = "评估项逻辑")
    private List<QuestionLogicSaveReqVO> questionLogic;

    @Schema(description = "评估项")
    @Valid
    private List<QuestionManagementSaveReqVO> questions;

    @Schema(description = "是否内置", example = "false")
    private Boolean builtIn;

    @Schema(description = "是否模板", example = "false")
    private Boolean isTemplate;

    @Schema(description = "模板类型，0我的，1系统", example = "0")
    private Integer templateType;

    @Schema(description = "更新模式，为1的时候为删除所有答卷的更新")
    private String updateMode;

    @Schema(description = "发布范围，-1为全校，其余情况为对应班级")
    private Long publishScale;

    @Schema(description = "班级对应的专题课")
    private List<ClassCourseDO> classCourse;
}

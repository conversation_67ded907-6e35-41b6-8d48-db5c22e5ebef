package com.unicom.swdx.module.edu.controller.admin.classcourse.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;

/**
* 班级课程安排 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class ClassCourseBaseVO {

    /**
     * 班级id
     */
    @ApiModelProperty(value = "班级id", required = true)
//    @NotNull(message = "班级id不能为空")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long classId;

    /**
     * 课程id
     */
    @ApiModelProperty(value = "课程id", required = true)
//    @NotNull(message = "课程id不能为空")
    private Long courseId;

    /**
     * 开始时间：传参格式（"beginTime": "2024-11-01 09:00:00"）
     */
    @ApiModelProperty(value = "开始时间", required = true)
//    @NotNull(message = "开始时间不能为空")
    private LocalDateTime beginTime;

    /**
     * 结束时间：传参格式（"beginTime": "2024-11-01 09:00:00"）
     */
    @ApiModelProperty(value = "结束时间", required = true)
//    @NotNull(message = "结束时间不能为空")
    private LocalDateTime endTime;

    /**
     * 教师id
     */
    @ApiModelProperty(value = "教师id", required = true)
//    @NotNull(message = "教师id不能为空")
    private Long teacherId;

    /**
     * 教室id
     */
    @ApiModelProperty(value = "教室id", required = true)
//    @NotNull(message = "教室id不能为空")
    private Long classroomId;

    /**
     * 是否暂存
     */
    @ApiModelProperty(value = "是否暂存", required = true)
//    @NotNull(message = "是否暂存不能为空")
    private Boolean isTemporary;

    /**
     * 是否合班授课
     */
    @ApiModelProperty(value = "是否合班授课", required = true)
//    @NotNull(message = "是否合班授课不能为空")
    private Boolean isMerge;

    /**
     * 是否厅级领导授课
     */
    @ApiModelProperty(value = "是否厅级领导授课", required = true)
    private Boolean isDepartmentLeader;

    /**
     * 是否调课
     */
    @ApiModelProperty(value = "是否调课", required = true)
//    @NotNull(message = "是否调课不能为空")
    private Boolean isChange;

    /**
     * 是否由配置模版生成的初始数据，默认为false
     */
    @ApiModelProperty(value = "是否由配置模版生成的初始数据，默认为false")
    private Boolean original;

    /**
     * 是否为部门授课，默认为教师授课
     */
    @ApiModelProperty(value = "是否为部门授课，默认为教师授课")
    private Boolean department;


    /**
     * 授课者字符串，id之间逗号间隔
     */
    @ApiModelProperty(value = "授课者字符串，id之间逗号间隔")
    private String teacherIdString;

    /**
     * 教学计划id
     */
    @ApiModelProperty(value = "教学计划id", required = true)
//    @NotNull(message = "教学计划id不能为空")
    private Long planId;

    /**
     * 日期
     */
    @ApiModelProperty(value = "日期", required = true)
    @NotNull(message = "日期不能为空")
    private String date;

    /**
     * 时间段（0上午，1下午，2晚上）
     */
    @ApiModelProperty(value = "时间段（0上午，1下午，2晚上）", required = true)
    @NotNull(message = "时间段（0上午，1下午，2晚上）不能为空")
    private String period;

    @ApiModelProperty(value = "冲突信息")
    private String conflictInfo;

    /**
     * 默认开启考勤
     */
    @ApiModelProperty(value = "是否开启考勤")
    private Boolean isCheck;


    /**
     * 部门授课时的部门Id
     */
    @ApiModelProperty(value = "部门授课时的部门Id")
    private Long deptId;

    /**
     * 是否党政领导讲课，0不是，1是
     */
    @ApiModelProperty(value = "是否党政领导讲课，0不是，1是")
    private Boolean isLeaderLecture;


}

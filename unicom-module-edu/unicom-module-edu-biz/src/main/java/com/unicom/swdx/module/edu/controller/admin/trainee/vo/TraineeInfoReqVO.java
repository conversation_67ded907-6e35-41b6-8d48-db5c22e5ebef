package com.unicom.swdx.module.edu.controller.admin.trainee.vo;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * @ClassName: RegistrationPageReqVO
 * @Author: lty
 * @Date: 2024/10/9 14:32
 */
@Data
@ApiModel(value = "报名详情分页返回VO")
public class TraineeInfoReqVO extends PageParam {
    @ApiModelProperty(value = "班级id")
    private Long classId;
    @ApiModelProperty("学员姓名")
    private String name;
    @ApiModelProperty("学员单位")
    private String unitName;

    @ApiModelProperty(value = "单位id")
    private Long unitId;

    @ApiModelProperty(value = "学员状态")
    private Integer status;

    @ApiModelProperty(value = "报到状态 1未报到 2已报到")
    private Integer reportStatus;

    @ApiModelProperty(value = "是否导出")
    private Set<Integer> includeColumnIndexes;

    private Integer orderStatus;

    private String type;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "班次名称")
    private String className;

    private Boolean isPlatform;

    @ApiModelProperty(value = "班级id")
    private List<Long> classIds;

    @ApiModelProperty(value = "班级属性")
    private Long classAttribute;

    @ApiModelProperty(value = "教师id")
    private Long teacherId;
}

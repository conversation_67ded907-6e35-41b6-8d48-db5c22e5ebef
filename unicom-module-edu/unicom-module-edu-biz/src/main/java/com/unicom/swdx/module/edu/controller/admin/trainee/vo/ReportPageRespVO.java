package com.unicom.swdx.module.edu.controller.admin.trainee.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.unicom.swdx.framework.mybatis.core.type.SM4EncryptTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * @ClassName: ReportPageRespVO
 * @Author: lty
 * @Date: 2024/10/9 14:32
 */
@Data
@ApiModel(value = "报名详情分页请求VO")
@TableName(value = "edu_trainee",autoResultMap = true)
public class ReportPageRespVO {

    @ApiModelProperty(value = "班级id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty(value = "序号")
    private Long index;

    @ApiModelProperty(value = "班级名称")
    private String className;

    @ApiModelProperty(value = "开班时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate openingTime;

    @ApiModelProperty(value = "已报到人数")
    private Integer reportNum;

    @ApiModelProperty(value = "未报到人数")
    private Integer notReportNum;

    @ApiModelProperty(value = "报到状态")
    private Integer reportStatus;

    @ApiModelProperty(value = "学员姓名")
    private String name;
    @ApiModelProperty(value = "学员手机号")
    private String phone;
    @ApiModelProperty(value = "学员身份证")
    @TableField(typeHandler = SM4EncryptTypeHandler.class)
    private String cardNo;
    @ApiModelProperty(value = "单位")
    private String unitName;
    @ApiModelProperty(value = "职务")
    private String position;
    @ApiModelProperty(value = "报名日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate createTime;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate reportDate;

    @ApiModelProperty(value = "班级属性")
    private String classAttribute;
}

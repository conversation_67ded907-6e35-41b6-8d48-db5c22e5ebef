package com.unicom.swdx.module.edu.controller.admin.courses.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 课程库 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class CoursesExcelVO {

    @ExcelProperty("课程名称")
    private String name;

    @ExcelProperty("授课教师")
    private String teacherNameList;

    @ExcelProperty("课程分类")
    private String theme;

    @ExcelProperty("教学形式")
    private String educateForm;

    @ExcelProperty("管理部门")
    private String managementDept;

    @ExcelProperty("课程状态")
    private String statusName;

}

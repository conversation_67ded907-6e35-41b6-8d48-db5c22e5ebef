package com.unicom.swdx.module.edu.controller.admin.traineegroup;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.unicom.swdx.module.edu.enums.traineeleave.TraineeLeaveStatus;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName: TraineeGroupReqVO
 * @Author: zhk
 * @Date: 2024/10/17 17:03
 */
@Data
public class AppTraineeGroupRespVO {
    /**
     * id
     */
    @ApiModelProperty(value = "id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    /**
     * 组名
     */
    @ApiModelProperty(value = "组名")
    private String groupName;
    /**
     * 学员姓名
     */
    @ApiModelProperty(value = "学员姓名")
    private String name;
    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    private String sex;
    /**
     * 电话号码
     */
    @ApiModelProperty(value = "电话号码")
    private String phone;
    /**
     * 学员职务
     */
    @ApiModelProperty(value = "学员职务")
    private String position;
    /**
     * 班委id
     */
    @ApiModelProperty(value = "班委id")
    private Long classCommitteeId;
    /**
     * 班委职务
     */
    @ApiModelProperty(value = "班委职务")
    private String classCommitteeName;
    /**
     * 学员请假状态 0草稿1已撤回2待审批3审批中4已通过5已拒绝
     * {@link TraineeLeaveStatus}
     */
    @ApiModelProperty(value = "学员请假状态 0草稿1已撤回2待审批3审批中4已通过5已拒绝")
    private Integer leaveStatus;


}

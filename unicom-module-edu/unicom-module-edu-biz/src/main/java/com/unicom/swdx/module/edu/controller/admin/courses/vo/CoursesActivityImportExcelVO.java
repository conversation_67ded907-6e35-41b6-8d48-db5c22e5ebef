package com.unicom.swdx.module.edu.controller.admin.courses.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.unicom.swdx.framework.excel.core.convert.TrimConvert;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 教学活动库 导入Excel VO
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
// 设置 chain = false，避免导入有问题
@Accessors(chain = false)
public class CoursesActivityImportExcelVO {

    @HeadStyle(fillForegroundColor = 13, horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ExcelProperty(value = "名称", index = 0, converter = TrimConvert.class)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    private String name;

    @HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ExcelProperty(value = "活动类型", index = 1)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    private String activityTypeName;

    @ExcelProperty(value = "管理部门", index = 2)
    @HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    private String managementDept;

    @ExcelProperty(value = "开发时间", index = 3)
    @HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    private String date;
}

package com.unicom.swdx.module.edu.controller.admin.teacherinformation;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.excel.core.util.ExcelUtils;
import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import com.unicom.swdx.framework.tenant.core.aop.TenantIgnore;
import com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo.businesscenter.TeachingHoursForBusinessCenterReqVO;
import com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo.businesscenter.TeachingHoursForBusinessCenterRespVO;
import com.unicom.swdx.module.edu.service.teacherinformation.TeacherDeptService;
import com.unicom.swdx.module.edu.utils.excel.ExcelImportResultRespVO;
import com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.electiverelease.ElectiveReleaseClassTimeTeacherReqVO;
import com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo.*;
import com.unicom.swdx.module.edu.convert.teacherinformation.TeacherInformationConvert;
import com.unicom.swdx.module.edu.dal.dataobject.teacherinformation.TeacherInformationDO;
import com.unicom.swdx.module.edu.service.teacherinformation.TeacherInformationService;
import com.unicom.swdx.module.edu.service.teacherinformation.TeacherInformationServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.time.LocalDate;
import java.time.Period;
import java.time.ZoneId;
import java.util.Collection;
import java.util.List;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.EXPORT;

@Api(tags = "管理后台 - 师资信息")
@RestController
@RequestMapping("/edu/teacherInformation")
@Validated
@Slf4j
public class TeacherInformationController {

    @Resource
    private TeacherInformationService teacherInformationService;

    @Resource
    private TeacherDeptService teacherDeptService;

    @PostMapping("/create")
    @ApiOperation("创建师资信息")
    @PreAuthorize("@ss.hasPermission('edu:teacher-information:create')")
    public CommonResult<Long> createTeacherInformation(@Valid @RequestBody TeacherInformationCreateReqVO createReqVO) {
        return success(teacherInformationService.createTeacherInformation(createReqVO));
    }

    @GetMapping("/export-import-template")
    @ApiOperation("校外师资导入模板导出")
    @PreAuthorize("@ss.hasPermission('edu:teacher-information:export')")
    @OperateLog(type = EXPORT)
    public void exportImportTemplate(HttpServletRequest request, HttpServletResponse response) throws IOException {
        teacherInformationService.exportImportTemplateNew(request, response);
    }

    @PostMapping("/import-teacher-information")
    @ApiOperation("导入校外师资")
    @PreAuthorize("@ss.hasPermission('edu:courses:import')")
    public CommonResult<ExcelImportResultRespVO> importTeacherInformation(HttpServletRequest request, @RequestParam("file") MultipartFile file) throws IOException, InterruptedException {
        List<TeacherInformationImportExcelVO> list = ExcelUtils.readFirstSheetAndCheckHead(file, TeacherInformationImportExcelVO.class);
        // 导入校外师资Excel
        ExcelImportResultRespVO coursesImportResultRespVO = teacherInformationService.importTeacherInformation(request, TeacherInformationConvert.INSTANCE.convertList02(list));
        return success(coursesImportResultRespVO);
    }

    @PostMapping("/import-teacher-new")
    @ApiOperation("导入校外师资")
    @PreAuthorize("@ss.hasPermission('edu:courses:import')")
    public CommonResult<ExcelImportResultRespVO> importTeacherNew( @Valid @RequestBody List<TeacherInformationImportNewExcelVO> list) throws IOException, InterruptedException {
        // 导入校外师资Excel
        ExcelImportResultRespVO excelImportResultRespVO = teacherInformationService.importTeacherInformationNew(TeacherInformationConvert.INSTANCE.convertList03(list));

        return success(excelImportResultRespVO);
    }

    @PostMapping("/check-teacher-information")
    @ApiOperation("校验校外师资课程是否匹配当前专题库课程")
    @PreAuthorize("@ss.hasPermission('edu:courses:import')")
    public CommonResult<TeacherInformationImportCheckVO> checkImportTeacherInformation(HttpServletRequest request, @RequestParam("file") MultipartFile file) throws IOException, InterruptedException {
        List<TeacherInformationImportNewExcelVO> list = ExcelUtils.readFirstSheetAndCheckHead(file, TeacherInformationImportNewExcelVO.class);

        TeacherInformationImportCheckVO teacherInformationImportCheckVO = teacherInformationService.checkImportTeacherInformation(list);

        return success(teacherInformationImportCheckVO);
    }



    @PostMapping("/update")
    @ApiOperation("更新师资信息")
    @PreAuthorize("@ss.hasPermission('edu:teacher-information:update')")
    public CommonResult<Boolean> updateTeacherInformation(@Valid @RequestBody TeacherInformationUpdateReqVO updateReqVO) {
        teacherInformationService.updateTeacherInformation(updateReqVO, true);
        return success(true);
    }

    @PostMapping("/delete")
    @ApiOperation("删除师资信息")
    @PreAuthorize("@ss.hasPermission('edu:teacher-information:delete')")
    public CommonResult<Boolean> deleteTeacherInformation(@RequestBody String params) {
        JSONObject paramsJSONObject = JSONObject.parseObject(params);
        Long id = paramsJSONObject.getLong("id");
        teacherInformationService.deleteTeacherInformation(id);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation("获得师资信息")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('edu:teacher-information:query')")
    public CommonResult<TeacherInformationRespVO> getTeacherInformation(@RequestParam("id") Long id) {
        TeacherInformationRespVO respVO = teacherInformationService.getTeacherInformation(id);

//        TeacherInformationRespVO respVO = TeacherInformationConvert.INSTANCE.convert(teacherInformation);

        return success(respVO);
    }

    @GetMapping("/getTeacher")
    @ApiOperation("获得师资信息")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('edu:teacher-information:query')")
    @PermitAll
    public CommonResult<TeacherInformationRespVO> getTeacherInformationPermitAll(@RequestParam("id") Long id) {
        TeacherInformationRespVO respVO = teacherInformationService.getTeacherInformation(id);

//        TeacherInformationRespVO respVO = TeacherInformationConvert.INSTANCE.convert(teacherInformation);

        return success(respVO);
    }

    @PostMapping("/list-for-elective-release")
    @ApiOperation("选修课管理-根据选修课发布上课时间段获取空闲下拉教师数据")
    @PreAuthorize("@ss.hasPermission('edu:teacher-information:query')")
    public CommonResult<List<TeacherInformationSimpleRespVO>> listForElectiveRelease(@Valid @RequestBody ElectiveReleaseClassTimeTeacherReqVO reqVO) {
        List<TeacherInformationSimpleRespVO> teacherInformationSimpleRespVOList = teacherInformationService.listForElectiveRelease(reqVO);
        return success(teacherInformationSimpleRespVOList);
    }

    @GetMapping("/getCourse")
    @ApiOperation("获得师资对应的课程信息")
    @ApiImplicitParam(name = "id", value = "老师id", required = true, example = "1024", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('edu:teacher-information:query')")
    public CommonResult<List<CourseInformationRespVO>> getCourseInformationByTeacher(@RequestParam("id") Long id) {
        List<CourseInformationRespVO> courseInformation = teacherInformationService.getCourseInformationByTeacher(id);
        return success(courseInformation);
    }

    @GetMapping("/list")
    @ApiOperation("获得师资信息列表")
    @ApiImplicitParam(name = "ids", value = "编号列表", required = true, example = "1024,2048", dataTypeClass = List.class)
    @PreAuthorize("@ss.hasPermission('edu:teacher-information:query')")
    public CommonResult<List<TeacherInformationRespVO>> getTeacherInformationList(@RequestParam("ids") Collection<Long> ids) {
        List<TeacherInformationDO> list = teacherInformationService.getTeacherInformationList(ids);
        if (list == null) {
            return null;
        }
        return success(TeacherInformationConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @ApiOperation("获得师资信息分页")
    @PreAuthorize("@ss.hasPermission('edu:teacher-information:query')")
    public CommonResult<PageResult<TeacherInformationRespVO>> getTeacherInformationPage(@Valid TeacherInformationPageReqVO pageVO) {
        PageResult<TeacherInformationRespVO> pageResult = teacherInformationService.getTeacherInformationPage(pageVO);
        return success(pageResult);
    }

    @GetMapping("/listAll")
    @ApiOperation("获得全校教师信息列表")
    @PreAuthorize("@ss.hasPermission('edu:teacher-information:query')")
    public CommonResult<List<TeacherInformationRespVO>> getTeacherInformationPage() {
        List<TeacherInformationRespVO> listResult = teacherInformationService.getTeacherInformationListAll();
        return success(listResult);
    }

    @GetMapping("/export")
    @ApiOperation("导出师资信息")
    @PreAuthorize("@ss.hasPermission('edu:teacher-information:export')")
    @OperateLog(type = EXPORT)
    public void exportTeacherInformationExcel(@Valid TeacherInformationPageReqVO exportReqVO, HttpServletResponse response) throws IOException {
        List<TeacherInformationExcelVO> list = teacherInformationService.getTeacherInformationExportList(exportReqVO);


        list.forEach(it->{

            try {
                String contactInformation = it.getContactInformation();
                if (contactInformation != null) {
                    String encryptedContactInformation = TeacherInformationServiceImpl.desensitizeDecrypt((contactInformation));
                    it.setContactInformation(encryptedContactInformation);
                }
            }catch (Exception e){
                e.printStackTrace();
            }

        });


        // 导出 Excel
        ExcelUtils.writeByIncludeColumnIndexes(response, "师资库.xls",
                "数据", TeacherInformationExcelVO.class, null, list, exportReqVO.getIncludeColumnIndexes());
    }

    @PostMapping("/sync")
    @ApiOperation("一键同步教师")
    @PermitAll
    @PreAuthorize("@ss.hasPermission('edu:teacher-information:sync')")
    public CommonResult<TeacherSyncResultRespVO> syncTeacher(HttpServletRequest request , @RequestParam(value = "tenantId" ,required = false) Long tenantId) {
        TeacherSyncResultRespVO syncResult = teacherInformationService.syncTeacher(request , tenantId);

        return success(syncResult);
    }

    @PostMapping("/syncDept")
    @ApiOperation("一键同步教师部门")
    @PreAuthorize("@ss.hasPermission('edu:teacher-information:sync')")
    @TenantIgnore
    public CommonResult<Boolean> syncTeacher(@RequestParam(value = "tenantId") Long tenantId) {
        teacherDeptService.syncTeacherDept(tenantId);
        return success(true);
    }


    @GetMapping("/delete-systemId")
    @ApiOperation("一键同步教师")
    @PermitAll
    @PreAuthorize("@ss.hasPermission('edu:teacher-information:sync')")
    public CommonResult<Boolean> deleteTeacherBySystemId(@RequestParam(value = "systemId" ,required = false) Long systemId) {
        //edu_teacher_information,system_users
        teacherInformationService.deleteTeacherBySystemId(systemId);
        return success(true);
    }

    /**
     * 教学培训-教师个人-仪表盘
     * @param userAppletBaseVO 开始时间+结束时间
     * @return 教学总工时+平均分+课程统计
     */
    @PostMapping("get-dashboard")
    @ApiOperation("教学培训-仪表盘")
//    @PreAuthorize("@ss.hasPermission('edu:teacher-information:query')")
    @PermitAll
    public CommonResult<UserAppletDashboardVO> getDashboard(@RequestBody UserAppletBaseVO userAppletBaseVO) {
        UserAppletDashboardVO userAppletDashboardVO = teacherInformationService.getDashboard(userAppletBaseVO);
        return success(userAppletDashboardVO);
    }


    @PostMapping("getTeachingHours")
    @ApiOperation("业中首页-仪表盘-教学课时")
    @PermitAll
    @TenantIgnore
    public CommonResult<TeachingHoursForBusinessCenterRespVO> getTeachingHours(@RequestBody TeachingHoursForBusinessCenterReqVO reqVO) {
        TeachingHoursForBusinessCenterRespVO vo = teacherInformationService.getTeachingHours(reqVO);
        return success(vo);
    }


    /**
     * 教学培训-教师个人-教学方式
     * @param reqVO 开始时间+结束时间
     * @return 教学方式统计
     */
    @PostMapping("get-teaching-methods")
    @ApiOperation("教学培训-教学方式")
    @PermitAll
    public CommonResult<List<UserAppletTeachingMethodsVO>> getTeachingMethods(@RequestBody UserAppletCourseTypeVO reqVO) {
        List<UserAppletTeachingMethodsVO> result = teacherInformationService.getTeachingMethods(reqVO);
        return success(result);
    }


    /**
     * 教学培训-课程情况
     * @param userAppletBaseVO 开始时间+结束时间
     * @return 课程情况
     */
    @PostMapping("get-course-situation")
    @ApiOperation("教学培训-课程情况")
    @PermitAll
    public CommonResult<UserAppletReultVO> getCourseSituation(@RequestBody UserAppletCourseSituationReqVO userAppletBaseVO) {
        UserAppletReultVO result = teacherInformationService.getCourseSituation(userAppletBaseVO);
        return success(result);
    }


    @GetMapping("/getTeacherBySystemId")
    @ApiOperation("获得师资信息")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermission('edu:teacher-information:query')")
    public CommonResult<TeacherInformationDO> getTeacherInformationBySystemId(@RequestParam("id") Long id) {

        TeacherInformationDO teacherInformationDO = teacherInformationService.getTeacherInformationBySystemId(id);

        return success(teacherInformationDO);
    }

}

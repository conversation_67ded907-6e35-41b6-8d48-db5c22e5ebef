package com.unicom.swdx.module.edu.controller.admin.trainee.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;


/**
 * @ClassName: RegistrationPageReqVO
 * @Author: lty
 * @Date: 2024/10/9 14:32
 */
@Data
@ApiModel(value = "报名详情分页返回VO")
public class ReportInfoExcelVO {
    @ExcelProperty(value = "序号")
    private Integer index;

    @ExcelProperty(value = "班次名称")
    private String className;

    @ExcelProperty(value = "班级属性")
    private String classAttribute;

    @ExcelProperty(value = "开班时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate openingTime;

    @ExcelProperty(value = "未已报到人数")
    private Integer notReportNum;

    @ExcelProperty(value = "已报到人数")
    private Integer reportNum;

}

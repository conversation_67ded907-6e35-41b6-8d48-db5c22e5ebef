package com.unicom.swdx.module.edu.controller.admin.trainee.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName: RegistrationPageReqVO
 * @Author: lty
 * @Date: 2024/10/9 14:32
 */
@Data
@ApiModel(value = "报名详情分页返回VO")
public class ReportInfoDetailExcelVO {
    @ExcelProperty(value = "序号")
    private Integer index;

    @ExcelProperty(value = "姓名")
    private String name;

    @ExcelProperty(value = "手机号")
    private String phone;

    @ExcelProperty(value = "班次名称")
    private String className;

    @ExcelProperty(value = "班级属性")
    private String classAttribute;

    @ExcelProperty(value = "学员身份证")
    private String cardNo;

    @ExcelProperty(value = "单位")
    private String unitName;

    @ExcelProperty(value = "职务")
    private String position;

    @ExcelProperty(value = "报到日期")
    private String reportDate;
}

package com.unicom.swdx.module.edu.controller.admin.clockininfo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.unicom.swdx.framework.common.util.date.DateUtils;
import com.unicom.swdx.framework.jackson.core.databind.LocalDateTimePatternSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR> <PERSON>he
 * @Description: 班主任移动端-班级考勤-学员考勤信息 Resp VO
 * @date 2024-11-12
 */
@ApiModel("班主任移动端-班级考勤-学员考勤信息 Resp VO")
@Data
public class AppTraineeAttendanceDetailsRespVO {


    @ApiModelProperty(value = "学员id", example = "1")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long traineeId;

    @ApiModelProperty(value = "组名", example = "第一组")
    private String groupName;

    @ApiModelProperty(value = "学员姓名", example = "张三")
    private String traineeName;

    @ApiModelProperty(value = "性别", example = "男")
    private String sex;

    @ApiModelProperty(value = "电话号码", example = "12345678901")
    private String phone;

    @ApiModelProperty(value = "学员职务", example = "班委")
    private String position;

    @ApiModelProperty(value = "班委id", example = "1")
    private Long classCommitteeId;

    @ApiModelProperty(value = "班委职务", example = "班委")
    private String classCommitteeName;

    @ApiModelProperty(value = "考勤记录id", example = "1")
    private Long recordId;

    @ApiModelProperty(value = "班级id", example = "1")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long classId;

    @ApiModelProperty(value = "排课表id", example = "1")
    private Long classCourseId;

    @ApiModelProperty(value = "考勤类型 0-到课 1-就餐 2-住宿", example = "0")
    private Integer type;

    @ApiModelProperty(value = "就餐时间段 0-早餐 1-午餐 2-晚餐", example = "1")
    private Integer mealPeriod;

    @ApiModelProperty(value = "学生状态 0-未打卡 1-已打卡 2-迟到 3-请假 null-全部", example = "0")
    private Integer traineeStatus;

    @ApiModelProperty(value = "打卡时间", example = "2024-10-10 09:00:00")
    @JsonSerialize(using = LocalDateTimePatternSerializer.class)
    private LocalDateTime clockingTime;

    @ApiModelProperty(value = "考勤日期", example = "2024-10-10")
    @JsonFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY)
    private LocalDate date;

    @ApiModelProperty(value = "学生请假类型 1-事假 2-病假 3-五会假", example = "0")
    private Integer leaveType;

    @ApiModelProperty(value = "学生请假状态 2-待审批 3-审批中 4-已通过", example = "0")
    private Integer leaveStatus;


}

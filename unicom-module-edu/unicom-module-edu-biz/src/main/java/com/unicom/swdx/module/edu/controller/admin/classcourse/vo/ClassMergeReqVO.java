package com.unicom.swdx.module.edu.controller.admin.classcourse.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Data
@ToString(callSuper = true)
public class ClassMergeReqVO {

    @ApiModelProperty(value = "课程id")
    @NotNull(message = "课程id不能为空")
    private Long courseId;

    @ApiModelProperty(value = "开始时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
//    @NotNull(message = "开始时间不能为空")
    private LocalDateTime beginTime;

    @ApiModelProperty(value = "结束时间")
//    @NotNull(message = "结束时间不能为空")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endTime;

    @ApiModelProperty(value = "授课者字符串，id之间逗号间隔")
    @NotNull(message = "授课者字符串不能为空")
    private String teacherIdString;

    @ApiModelProperty(value = "日期")
    private String date;

    @ApiModelProperty(value = "午别")
    private String period;
}

package com.unicom.swdx.module.edu.service.completiontemplate;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.mybatis.core.util.MyBatisUtils;
import com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils;
import com.unicom.swdx.module.edu.constant.CompletionTemplateConstant;
import com.unicom.swdx.module.edu.controller.admin.classcompletiontemplate.vo.ClassCompletionTemplateCreateReqVO;
import com.unicom.swdx.module.edu.controller.admin.clockininfo.dto.attendancerate.TraineeAttendanceInfoDTO;
import com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.ClockInInfoPageReqVO;
import com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.ClockInInfoStudentStatusVO;
import com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.attendancerate.AttendanceRateReqVO;
import com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.attendancerate.AttendanceRateTraineeInfoVO;
import com.unicom.swdx.module.edu.controller.admin.completiontemplate.vo.*;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.classevaluationstats.TraineeEvaluationDetailPageReqVO;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.classevaluationstats.TraineeEvaluationDetailPageRespVO;
import com.unicom.swdx.module.edu.controller.admin.traineeleave.vo.TraineeLeavePageReqVO;
import com.unicom.swdx.module.edu.controller.admin.traineeleave.vo.TraineeLeaveRespVO;
import com.unicom.swdx.module.edu.convert.classcompletion.ClassCompletionConvert;
import com.unicom.swdx.module.edu.convert.completiontemplate.CompletionTemplateConvert;
import com.unicom.swdx.module.edu.dal.dataobject.classcompletion.ClassCompletionDO;
import com.unicom.swdx.module.edu.dal.dataobject.classcompletiontemplate.ClassCompletionTemplateDO;
import com.unicom.swdx.module.edu.dal.dataobject.classmanagement.ClassManagementDO;
import com.unicom.swdx.module.edu.dal.dataobject.completiontemplate.CompletionTemplateDO;
import com.unicom.swdx.module.edu.dal.dataobject.teacherinformation.TeacherInformationDO;
import com.unicom.swdx.module.edu.dal.dataobject.training.TraineeDO;
import com.unicom.swdx.module.edu.dal.mysql.classcompletion.ClassCompletionMapper;
import com.unicom.swdx.module.edu.dal.mysql.classcompletiontemplate.ClassCompletionTemplateMapper;
import com.unicom.swdx.module.edu.dal.mysql.classmanagement.ClassManagementMapper;
import com.unicom.swdx.module.edu.dal.mysql.completiontemplate.CompletionTemplateMapper;
import com.unicom.swdx.module.edu.dal.mysql.teacherinformation.TeacherInformationMapper;
import com.unicom.swdx.module.edu.enums.classcompletion.DataSourceEnum;
import com.unicom.swdx.module.edu.enums.classcompletion.DefaultCompletionEnum;
import com.unicom.swdx.module.edu.enums.traineeleave.LeaveTaskStatus;
import com.unicom.swdx.module.edu.enums.traineeleave.TraineeLeaveType;
import com.unicom.swdx.module.edu.service.classcompletiontemplate.ClassCompletionTemplateServiceImpl;
import com.unicom.swdx.module.edu.service.clockininfo.ClockInInfoService;
import com.unicom.swdx.module.edu.service.evaluationresponse.EvaluationResponseService;
import com.unicom.swdx.module.edu.service.traineeleave.TraineeLeaveService;
import com.unicom.swdx.module.edu.service.training.TraineeServiceImpl;
import com.unicom.swdx.module.system.api.dict.DictDataApi;
import com.unicom.swdx.module.system.api.dict.dto.DictDataRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils.getTenantId;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.*;

/**
 * 结业考核模版设置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class CompletionTemplateServiceImpl implements CompletionTemplateService {

    @Resource
    private CompletionTemplateMapper completionTemplateMapper;

    @Resource
    private ClassManagementMapper classManagementMapper;

    @Resource
    private TraineeServiceImpl traineeService;

    @Resource
    private ClassCompletionMapper classCompletionMapper;

    @Resource
    private ClassCompletionTemplateServiceImpl classCompletionTemplateService;

    @Resource
    private ClassCompletionTemplateMapper classCompletionTemplateMapper;

    @Resource
    private ClockInInfoService clockInInfoService;

    @Resource
    private TraineeLeaveService leaveService;

    @Resource
    private EvaluationResponseService evaluationResponseService;

    @Resource
    private TeacherInformationMapper teacherInformationMapper;
    @Qualifier("com.unicom.swdx.module.system.api.dict.DictDataApi")
    @Autowired
    private DictDataApi dictDataApi;

    @Override
    public Integer createCompletionTemplate(CompletionTemplateCreateReqVO createReqVO) {
        // 插入
        CompletionTemplateDO completionTemplate = CompletionTemplateConvert.INSTANCE.convert(createReqVO);
        completionTemplateMapper.insert(completionTemplate);
        // 返回
        return completionTemplate.getId();
    }

    /**
     * 创建结业考核模版设置
     *
     * @param addReqVO 创建信息
     * @return 编号
     */
    @Override
    public List<Integer> addCompletionTemplate(List<CompletionTemplateCreateReqVO> addReqVO) {

        // 生成一个随机的 UUID
        UUID uuid = UUID.randomUUID();
        String uniqueCode = uuid.toString();

        // 模版名称唯一性校验
        campusNameRules(null, addReqVO.get(0).getTemplateName());

        // 同校区 默认规则只能拥有一个
        campusDefaultRules(null, addReqVO.get(0).getDefaultRule(), addReqVO.get(0).getCampus());


        // 新建一个数组
        List<CompletionTemplateDO> completionTemplateList = new ArrayList<>();

        for (CompletionTemplateCreateReqVO createReqVO : addReqVO) {
            createReqVO.setIdCode(uniqueCode);
            CompletionTemplateDO completionTemplate = CompletionTemplateConvert.INSTANCE.convert(createReqVO);
            completionTemplateList.add(completionTemplate);
        }

        // 批量插入
        completionTemplateMapper.insertBatch(completionTemplateList);

        // 返回插入记录的ID列表
        List<Integer> ids = new ArrayList<>();
        for (CompletionTemplateDO completionTemplate : completionTemplateList) {
            ids.add(completionTemplate.getId());
        }

        return ids;
    }

    @Override
    public void updateCompletionTemplate(CompletionTemplateUpdateReqVO updateReqVO) {

        //先删后插  下下策

        // 拿到所有数据  存在就修改，不存在就插入
        //根据主键 id 判断

        // 校验存在
        this.validateCompletionTemplateExists(updateReqVO.getId());
        // 更新
        CompletionTemplateDO updateObj = CompletionTemplateConvert.INSTANCE.convert(updateReqVO);
        completionTemplateMapper.updateById(updateObj);
    }

    /**
     * 更新结业考核模版设置
     *
     * @param editReqVO 更新信息
     */
    @Override
    public void updateCompletionTemplateData(List<CompletionTemplateUpdateReqVO> editReqVO) {

        //保存改数据的 idCode
        String idCode = editReqVO.get(0).getIdCode();


        List<CompletionTemplateDO> list = completionTemplateMapper.selectByTemplateCode(idCode);
        if (!list.isEmpty()) {
            //默认模板需要删除数据
            ClassCompletionTemplateCreateReqVO reqVO = new ClassCompletionTemplateCreateReqVO();
            reqVO.setIdCode(idCode);
            reqVO.setCampus(editReqVO.get(0).getCampus());

            reqVO.setDefaultRule(list.get(0).getDefaultRule());
            classCompletionTemplateService.deleteCompletionInfo(reqVO);
        }


        // 模版名称唯一性校验
        editNameRules(idCode, editReqVO.get(0).getTemplateName());

        // 同校区 默认规则只能拥有一个
        editDefaultRules(editReqVO.get(0).getDefaultRule(), editReqVO.get(0).getCampus(), editReqVO.get(0).getIdCode());

        //先删后插
        completionTemplateMapper.deleteCompletionTemplateByName(idCode);


        // 批量插入
        List<CompletionTemplateDO> completionTemplateList = new ArrayList<>();

        for (CompletionTemplateUpdateReqVO editVO : editReqVO) {
            editVO.setIdCode(idCode);
            CompletionTemplateDO completionTemplate = CompletionTemplateConvert.INSTANCE.convert(editVO);
            completionTemplateList.add(completionTemplate);
        }

        // 批量插入
        completionTemplateMapper.insertBatch(completionTemplateList);

        ClassCompletionTemplateCreateReqVO classCompletionTemplateCreateReqVO = new ClassCompletionTemplateCreateReqVO();
        classCompletionTemplateCreateReqVO.setIdCode(idCode);

        classCompletionTemplateService.createClassCompletionTemplate(classCompletionTemplateCreateReqVO);



    }

    @Override
    public void deleteCompletionTemplate(Integer id) {
        // 校验存在
        this.validateCompletionTemplateExists(id);
        // 删除
        completionTemplateMapper.deleteById(id);
    }

    /**
     * 删除模版
     *
     * @param idCode 模版名称
     */
    @Override
    public void deleteCompletionTemplateByName(String idCode) {

        //默认模板无法删除
        List<CompletionTemplateDO> list = completionTemplateMapper.selectOneByIdCode(idCode);

        if (list.isEmpty()){
            //模板不存在
            throw exception(TEMPLATE_NOT_EXISTS);
        }

        //默认模板
        if (Objects.equals(list.get(0).getDefaultRule(),0)){
            throw exception(DEFAULT_TEMPLATE);
        }

        //判断是否有班级在绑定使用
        int count  = completionTemplateMapper.selectCountByIdCode(idCode);
        if(count > 0){
            throw exception(COMPLETION_TEMPLATE_CLASS_EXISTS);
        }

        completionTemplateMapper.deleteCompletionTemplateByName(idCode);
    }

    /**
     * 批量删除
     *
     * @param idCode 模版名称
     */
    @Override
    public void deleteCompletionTemplateByNameBatch(List<String> idCode) {

        for(String id : idCode){
            //判断是否有班级在绑定使用
            int count  = completionTemplateMapper.selectCountByIdCode(id);
            if(count > 0){
                throw exception(COMPLETION_TEMPLATE_CLASS_BAN_EXISTS);
            }
        }


        for(String id : idCode){
            completionTemplateMapper.deleteCompletionTemplateByName(id);
        }

    }

    private void validateCompletionTemplateExists(Integer id) {
        if (completionTemplateMapper.selectById(id) == null) {
            throw exception(COMPLETION_TEMPLATE_NOT_EXISTS);
        }
    }

    @Override
    public CompletionTemplateDO getCompletionTemplate(Integer id) {
        return completionTemplateMapper.selectById(id);
    }

    @Override
    public List<CompletionTemplateDO> getCompletionTemplateList(String idCode) {

        return completionTemplateMapper.selectOneByIdCode(idCode);
    }

    @Override
    public PageResult<CompletionTemplateDO> getCompletionTemplatePage(HttpServletRequest request,CompletionTemplatePageReqVO pageReqVO) {

        //判断该账户是否是学员
        Long userIds = SecurityFrameworkUtils.getLoginUserId();

        if(!traineeService.selectTraineeByUserId(request,userIds)){
            throw exception(NO_PERMISSION_ERROR);
        }

        //替换掉特殊符号  %  _
        if (StringUtils.isNotBlank(pageReqVO.getTemplateName())){
            pageReqVO.setTemplateName(pageReqVO.getTemplateName().replaceAll("([%_])", "\\\\$1"));
        }

        Page buildPage = MyBatisUtils.buildPage(pageReqVO);

        List<CompletionTemplateDO> list = completionTemplateMapper.selectPageList(buildPage, pageReqVO);

        PageResult<CompletionTemplateDO> pageList = new PageResult<>(list, buildPage.getTotal());

        return pageList;
    }

    @Override
    public List<CompletionTemplateDO> getCompletionTemplatePageList(HttpServletRequest request, CompletionTemplatePageReqVO pageReqVO) {

        //判断该账户是否是学员
        Long userIds = SecurityFrameworkUtils.getLoginUserId();

        if(!traineeService.selectTraineeByUserId(request,userIds)){
            throw exception(NO_PERMISSION_ERROR);
        }

        //替换掉特殊符号  %  _
        if (StringUtils.isNotBlank(pageReqVO.getTemplateName())){
            pageReqVO.setTemplateName(pageReqVO.getTemplateName().replaceAll("([%_])", "\\\\$1"));
        }


        List<CompletionTemplateDO> list = completionTemplateMapper.selectPageListAll(pageReqVO);


        return list;
    }

    @Override
    public List<CompletionTempleExcelVO> getCompletionTemplateListExport(CompletionTemplateExportReqVO exportReqVO) {

        //替换掉特殊符号  %  _
        if (StringUtils.isNotBlank(exportReqVO.getTemplateName())){
            exportReqVO.setTemplateName(exportReqVO.getTemplateName().replaceAll("([%_])", "\\\\$1"));
        }

        //数据库查询的数据 与分页接口保持一致
        List<CompletionTemplateDO> list = completionTemplateMapper.selectListAll(exportReqVO);

        //导出类 数列表
        List<CompletionTempleExcelVO> exportList = new ArrayList<>();

        //选中导出
        if(exportReqVO.getIdList() != null){

            List<CompletionTemplateDO> listIdList = new ArrayList<>();

            for (CompletionTemplateDO vo : list){

                for (String idCode : exportReqVO.getIdList()){
                    if(vo.getIdCode().equals(idCode)){
                        listIdList.add(vo);
                    }
                }
            }


            for(CompletionTemplateDO vo : listIdList){
                CompletionTempleExcelVO export = new CompletionTempleExcelVO();

                //模版名称
                export.setTemplateName(vo.getTemplateName());

                //去字典表中查 校区  id  导出
                String campus = classManagementMapper.getDictLabelById(vo.getCampus());
                export.setCampus(campus);

                //默认值
                if(vo.getDefaultRule() == 0){
                    export.setDefaultRule("是");
                }else{
                    export.setDefaultRule("否");
                }

                exportList.add(export);
            }


        }else {
            for (CompletionTemplateDO vo : list){

                CompletionTempleExcelVO export = new CompletionTempleExcelVO();

                //模版名称
                export.setTemplateName(vo.getTemplateName());

                //去字典表中查 校区  id  导出
                String campus = classManagementMapper.getDictLabelById(vo.getCampus());
                export.setCampus(campus);

                //默认值
                if(vo.getDefaultRule() == 0){
                    export.setDefaultRule("是");
                }else{
                    export.setDefaultRule("否");
                }

                exportList.add(export);

            }
        }



        return exportList;
    }

    @Override
    public void exportExcel(HttpServletRequest request,Integer classId,Integer type,HttpServletResponse response) throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("成绩单");

        List<ClassCompletionTemplateDO> list = getCompletionTemplateList(classManagementMapper.selectById(classId));

        List<String> lastHeaders = list.stream()
                .filter(item -> item.getSerialNumber().contains("S"))
                .map(ClassCompletionTemplateDO::getColumnName)
                .collect(Collectors.toList());

        // 构建表头
        List<String> fixedHeaders = Arrays.asList("学员id","学生姓名", "单位职务", "班内职务");
        List<Map<String, Map<String, List<String>>>> dynamicHeaders = getDynamicHeaders(classId,list);

//        List<String> lastHeaders = Arrays.asList("获奖情况", "量化得分");

        buildHeader(sheet, fixedHeaders, dynamicHeaders, lastHeaders);

        int totalSize = 0; // 用于统计所有里层列表的总大小
        for (Map<String, Map<String, List<String>>> outerMap : dynamicHeaders) {
            for (Map<String, List<String>> middleMap : outerMap.values()) {
                for (List<String> innerList : middleMap.values()) {
                    totalSize += innerList.size(); // 获取最里层列表的大小
                }
            }
        }
        // 填充学生数据
        List<Map<String, Object>> students = getStudentData(request,classId);
        fillStudentData(workbook,sheet, students,type ,totalSize + lastHeaders.size() + 3);

        // 设置响应头并写入数据
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment;filename=成绩单.xlsx");
        workbook.write(response.getOutputStream());
        workbook.close();
    }

    @Override
    public ClassCompletionRespVO getClassCompletion(Long classId) {
        //根据classId获取模板

        ClassCompletionRespVO respVO = new ClassCompletionRespVO();

        List<ClassCompletionTemplateDO> list;

        ClassManagementDO classDO = classManagementMapper.selectById(classId);

        if (StrUtil.isNotBlank(classDO.getIdCode())){
//            list = completionTemplateMapper.selectByTemplateCode(classDO.getIdCode());
            list = classCompletionTemplateMapper.selectByTemplateCode(classDO.getId(),classDO.getIdCode());
        }else {
            //根据班级id查询对应的表头内容
            list = completionTemplateMapper.selectClassDefaultRule(0, classDO.getCampus());
        }
//        list.sort(Comparator.comparing(ClassCompletionTemplateDO::getSerialNumber));
//        list.removeIf(item -> item.getModuleName() == null);
        List<TraineeTreeBuilder.TreeNode> treeNodes = TraineeTreeBuilder.buildTree(list);

        respVO.setTreeNodes(treeNodes);



        return respVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void regenerate(Long classId) {
        //如果模板已经变更，刷新模板

        ClassManagementDO classDO = classManagementMapper.selectById(classId);
        if (Objects.isNull(classDO)){
            return;
        }

        //根据班级id获取结业考核表中学员信息
        //查询是否有暂存的，有才进行更新，否则直接退出
        List<ClassCompletionTemplateDO> list = classCompletionTemplateMapper.selectTemplateByIdCode(classDO.getId());

        if (!list.isEmpty()){
            //删除正在使用的模板
            classCompletionTemplateMapper.deleteTemplate(classDO.getId());

            //更新暂存模板
            classCompletionTemplateMapper.updateTemplate(classDO.getId());
        }

        // 删除已经存在的学员数据
        List<Long> ids = classCompletionMapper.listByClassId(classDO.getId())
                .stream()
                .map(ClassCompletionDO::getId)
                .collect(Collectors.toList());

        if (!ids.isEmpty()){
            classCompletionMapper.deleteBatchIds(ids);
        }

    }

    @Override
    public List<ClassCompletionInfoRespVO> getClassCompletionInfo(HttpServletRequest request, Integer classId) {
        // 查询班级信息
        ClassManagementDO classDO = classManagementMapper.selectById(classId);

        // 获取学员信息（如果班级有绑定模板则使用绑定模板，否则使用默认模板）
        List<ClassCompletionDO> traineeInfoList = getTraineeInfoList(classDO, classId);
        Map<Long, List<ClassCompletionDO>> traineeInfoMap = groupByTraineeId(traineeInfoList);

        // 获取相关数据（请假、考勤、迟到、评价等）
        Map<Long, List<TraineeLeaveRespVO>> leaveMap = getLeaveMap(classId);
        Map<Long, List<AttendanceRateTraineeInfoVO>> attendanceMap = getAttendanceMap(classId);
        Map<Long, List<ClockInInfoStudentStatusVO>> lateMap = getLateMap(request, classId);
        Map<Long, List<TraineeEvaluationDetailPageRespVO>> evaluationMap = getEvaluationMap(classId);

        // 构建返回结果
        return buildResponse(traineeInfoMap, leaveMap, attendanceMap, lateMap, evaluationMap);
    }

    /**
     * 获取学员信息列表
     */
    private List<ClassCompletionDO> getTraineeInfoList(ClassManagementDO classDO, Integer classId) {
        if (StrUtil.isNotBlank(classDO.getIdCode())) {
            return classCompletionMapper.getInfoByClassId(classId);
        } else {
            return classCompletionMapper.getDefaultRuleInfoByClassId(classDO);
        }
    }

    /**
     * 按 TraineeId 进行分组
     */
    private Map<Long, List<ClassCompletionDO>> groupByTraineeId(List<ClassCompletionDO> traineeInfoList) {
        return traineeInfoList.stream()
                .collect(Collectors.groupingBy(ClassCompletionDO::getTraineeId, LinkedHashMap::new, Collectors.toList()));
    }

    /**
     * 获取请假数据并按学员 ID 分组
     */
    private Map<Long, List<TraineeLeaveRespVO>> getLeaveMap(Integer classId) {
        List<TraineeLeaveRespVO> allLeaveList = leaveService.pageList(buildLeaveReqVO(classId, 10000)).getList();
        return allLeaveList.stream().collect(Collectors.groupingBy(TraineeLeaveRespVO::getTraineeId));
    }

    /**
     * 获取考勤数据并按学员 ID 分组
     */
    private Map<Long, List<AttendanceRateTraineeInfoVO>> getAttendanceMap(Integer classId) {
        List<AttendanceRateTraineeInfoVO> allAttendanceRates = clockInInfoService
                .getListForAttendanceThreeRate(buildAttendanceReqVO(classId))
                .getTraineeInfoList();
        return allAttendanceRates.stream().collect(Collectors.groupingBy(AttendanceRateTraineeInfoVO::getTraineeId));
    }

    /**
     * 获取迟到数据并按学员 ID 分组
     */
    private Map<Long, List<ClockInInfoStudentStatusVO>> getLateMap(HttpServletRequest request, Integer classId) {
        List<ClockInInfoStudentStatusVO> allLateInfo = clockInInfoService
                .getStudentClockingStatusPage(request, buildLateReqVO(classId))
                .getList();
        return allLateInfo.stream().collect(Collectors.groupingBy(ClockInInfoStudentStatusVO::getTraineeId));
    }

    /**
     * 获取学员评价数据并按学员 ID 分组
     */
    private Map<Long, List<TraineeEvaluationDetailPageRespVO>> getEvaluationMap(Integer classId) {
        List<TraineeEvaluationDetailPageRespVO> allEvaluationDetails = evaluationResponseService
                .getTraineeEvaluationDetailPage(buildEvaluationReqVO(classId))
                .getList();
        return allEvaluationDetails.stream().collect(Collectors.groupingBy(TraineeEvaluationDetailPageRespVO::getTraineeId));
    }

    /**
     * 组装返回数据
     */
    private List<ClassCompletionInfoRespVO> buildResponse(
            Map<Long, List<ClassCompletionDO>> traineeInfoMap,
            Map<Long, List<TraineeLeaveRespVO>> leaveMap,
            Map<Long, List<AttendanceRateTraineeInfoVO>> attendanceMap,
            Map<Long, List<ClockInInfoStudentStatusVO>> lateMap,
            Map<Long, List<TraineeEvaluationDetailPageRespVO>> evaluationMap) {

        List<ClassCompletionInfoRespVO> respVOS = new ArrayList<>();

        traineeInfoMap.forEach((traineeId, infoList) -> {
            ClassCompletionInfoRespVO respVO = new ClassCompletionInfoRespVO();
            Map<String, String> scoreMap = new LinkedHashMap<>();

            for (ClassCompletionDO info : infoList) {
                setBasicInfo(respVO, info);
                String score = getScoreByDataSource(info, leaveMap, attendanceMap, lateMap, evaluationMap);
                scoreMap.put(info.getSerialNumber(), score);
            }
            respVO.setMapScore(scoreMap);
            respVOS.add(respVO);
        });

        return respVOS;
    }

    /**
     * 设置学员的基本信息
     */
    private void setBasicInfo(ClassCompletionInfoRespVO respVO, ClassCompletionDO info) {
        respVO.setId(info.getId());
        respVO.setTraineeId(info.getTraineeId());
        respVO.setTraineeName(info.getTraineeName());
        respVO.setGroupName(info.getGroupName());
        respVO.setUnitPosition(info.getUnitPosition());
        respVO.setClassPosition(info.getClassPosition());
    }

//    @Override
//    public List<ClassCompletionInfoRespVO> getClassCompletionInfo(HttpServletRequest request,Integer classId) {
//        // 一次性查询所有需要的数据
//
//        //如果该班没有绑定模板则查默认模板，否则查班级绑定的模板
//        //查出班级
//        ClassManagementDO classDO = classManagementMapper.selectById(classId);
//
//        List<ClassCompletionDO> traineeInfoList;
//        if (StrUtil.isNotBlank(classDO.getIdCode())){
//            traineeInfoList = classCompletionMapper.getInfoByClassId(classId);
//        }else {
//            traineeInfoList = classCompletionMapper.getDefaultRuleInfoByClassId(classDO);
//        }
//
//        // 按 TraineeId 分组
//        Map<Long, List<ClassCompletionDO>> traineeInfoMap = traineeInfoList.stream()
//                .collect(Collectors.groupingBy(
//                        ClassCompletionDO::getTraineeId,
//                        LinkedHashMap::new,
//                        Collectors.toList()
//                ));
//
//
//        // 批量获取数据
//        List<TraineeLeaveRespVO> allLeaveList = leaveService.pageList(buildLeaveReqVO(classId, 10000)).getList();
//        Map<Long, List<TraineeLeaveRespVO>> leaveMap = allLeaveList.stream()
//                .collect(Collectors.groupingBy(TraineeLeaveRespVO::getTraineeId));
//
//        List<AttendanceRateTraineeInfoVO> allAttendanceRates = clockInInfoService
//                .getListForAttendanceThreeRate(buildAttendanceReqVO(classId))
//                .getTraineeInfoList();
//        Map<Long, List<AttendanceRateTraineeInfoVO>> attendanceMap = allAttendanceRates.stream()
//                .collect(Collectors.groupingBy(AttendanceRateTraineeInfoVO::getTraineeId));
//
//        List<ClockInInfoStudentStatusVO> allLateInfo = clockInInfoService.getStudentClockingStatusPage(request,buildLateReqVO(classId)).getList();
//        Map<Long, List<ClockInInfoStudentStatusVO>> lateMap = allLateInfo.stream()
//                .collect(Collectors.groupingBy(ClockInInfoStudentStatusVO::getTraineeId));
//
//        List<TraineeEvaluationDetailPageRespVO> allEvaluationDetails = evaluationResponseService
//                .getTraineeEvaluationDetailPage(buildEvaluationReqVO(classId))
//                .getList();
//        Map<Long, List<TraineeEvaluationDetailPageRespVO>> evaluationMap = allEvaluationDetails.stream()
//                .collect(Collectors.groupingBy(TraineeEvaluationDetailPageRespVO::getTraineeId));
//
//        // 构建返回结果
//        List<ClassCompletionInfoRespVO> respVOS = new ArrayList<>();
//        traineeInfoMap.forEach((traineeId, infoList) -> {
//            ClassCompletionInfoRespVO respVO = new ClassCompletionInfoRespVO();
//            Map<String, String> scoreMap = new LinkedHashMap<>();
//
//            for (ClassCompletionDO info : infoList) {
//                respVO.setId(info.getId());
//                respVO.setTraineeId(info.getTraineeId());
//                respVO.setTraineeName(info.getTraineeName());
//                respVO.setGroupName(info.getGroupName());
//                respVO.setUnitPosition(info.getUnitPosition());
//                respVO.setClassPosition(info.getClassPosition());
//
//                // 根据数据源处理逻辑
//                String score = getScoreByDataSource(info, leaveMap, attendanceMap, lateMap, evaluationMap);
//                scoreMap.put(info.getSerialNumber(), score);
//            }
//            respVO.setMapScore(scoreMap);
//            respVOS.add(respVO);
//        });
//
//        return respVOS;
//    }

//    private String getScoreByDataSource(ClassCompletionDO info,
//                                        Map<Long, List<TraineeLeaveRespVO>> leaveMap,
//                                        Map<Long, List<AttendanceRateTraineeInfoVO>> attendanceMap,
//                                        Map<Long, List<ClockInInfoStudentStatusVO>> lateMap,
//                                        Map<Long, List<TraineeEvaluationDetailPageRespVO>> evaluationMap) {
//        if (info.getScore() != null){
//            return info.getScore();
//        }
//        if (info.getAcquisitionMode() != null && info.getAcquisitionMode() == 1) {
//            if (info.getDataSource() >= DataSourceEnum.PERSONAL_LEAVE_NUM.getCode() && info.getDataSource() <= DataSourceEnum.FIVE_CANS_LEAVE_DAYS.getCode()) {
//
//                List<TraineeLeaveRespVO> leaves = leaveMap.get(info.getTraineeId());
//
//                if (CollUtil.isEmpty(leaves)){
//                    return "0";
//                }
//
//                if (Objects.equals(info.getDataSource(),DataSourceEnum.PERSONAL_LEAVE_NUM.getCode())){ //事假次数
//                    List<TraineeLeaveRespVO> tmpLeave = leaves.stream()
//                            .filter(leave -> Objects.equals(leave.getLeaveType(), TraineeLeaveType.BUSINESS.getCode()))
//                            .collect(Collectors.toList());
//                    return String.valueOf(tmpLeave.size());
//                }else if (Objects.equals(info.getDataSource(),DataSourceEnum.SICK_LEAVE_NUM.getCode())){//病假次数
//                    List<TraineeLeaveRespVO> tmpLeave = leaves.stream()
//                            .filter(leave -> Objects.equals(leave.getLeaveType(), TraineeLeaveType.SICK.getCode()))
//                            .collect(Collectors.toList());
//                    return String.valueOf(tmpLeave.size());
//                }else if (Objects.equals(info.getDataSource(),DataSourceEnum.FIVE_CANS_LEAVE_NUM.getCode())){//五会假次数
//                    List<TraineeLeaveRespVO> tmpLeave = leaves.stream()
//                            .filter(leave -> Objects.equals(leave.getLeaveType(), TraineeLeaveType.FIVE_CANS.getCode()))
//                            .collect(Collectors.toList());
//                    return String.valueOf(tmpLeave.size());
//                } else if (Objects.equals(info.getDataSource(), DataSourceEnum.PERSONAL_LEAVE_DAYS.getCode())) {
//                    double days = leaves.stream()
//                            .filter(leave -> Objects.equals(leave.getLeaveType(), TraineeLeaveType.BUSINESS.getCode()))
//                            .mapToDouble(TraineeLeaveRespVO::getDays)
//                            .sum();
//                    return String.format("%.1f", days);
//                } else if (Objects.equals(info.getDataSource(), DataSourceEnum.SICK_LEAVE_DAYS.getCode())) {
//                    double days = leaves.stream()
//                            .filter(leave -> Objects.equals(leave.getLeaveType(), TraineeLeaveType.SICK.getCode()))
//                            .mapToDouble(TraineeLeaveRespVO::getDays)
//                            .sum();
//                    return String.format("%.1f", days);
//                } else if (Objects.equals(info.getDataSource(),DataSourceEnum.FIVE_CANS_LEAVE_DAYS.getCode())){//五会假天数
//                    double days = leaves.stream()
//                            .filter(leave -> Objects.equals(leave.getLeaveType(), TraineeLeaveType.FIVE_CANS.getCode()))
//                            .mapToDouble(TraineeLeaveRespVO::getDays)
//                            .sum();
//                    return String.format("%.1f", days);
//                }
//
////                return String.valueOf(leaves != null ? leaves.size() : 0);
//            } else if (info.getDataSource() >= DataSourceEnum.CLASS_ATTENDANCE_RATE.getCode() && info.getDataSource() <= DataSourceEnum.ACCOMMODATION_RATE.getCode()) {
//                List<AttendanceRateTraineeInfoVO> rates = attendanceMap.get(info.getTraineeId());
//                if (rates != null && !rates.isEmpty()) {
//                    AttendanceRateTraineeInfoVO rateInfo = rates.get(0);
//                    if (Objects.equals(info.getDataSource(),DataSourceEnum.CLASS_ATTENDANCE_RATE.getCode())) return rateInfo.getClassRate();
//                    if (Objects.equals(info.getDataSource(),DataSourceEnum.MEAL_ATTENDANCE_RATE.getCode())) return rateInfo.getMealRate();
//                    if (Objects.equals(info.getDataSource(),DataSourceEnum.ACCOMMODATION_RATE.getCode())) return rateInfo.getAccommodationRate();
//                }
//            } else if (Objects.equals(info.getDataSource(),DataSourceEnum.COURSE_EVALUATION_RATE.getCode())) {
//                List<TraineeEvaluationDetailPageRespVO> evaluations = evaluationMap.get(info.getTraineeId());
//                if (evaluations != null && !evaluations.isEmpty()) {
//                    return String.format("%.2f", evaluations.get(0).getRatio() * 100);
//                }
//            } else if (Objects.equals(info.getDataSource(),DataSourceEnum.LATE_COUNT.getCode())) {
//                List<ClockInInfoStudentStatusVO> lateInfo = lateMap.get(info.getTraineeId());
//                return String.valueOf(lateInfo != null ? lateInfo.size() : 0);
//            }
//        }
//        return info.getScore() == null ? "0" : info.getScore();
//    }


    private String getScoreByDataSource(ClassCompletionDO info,
                                        Map<Long, List<TraineeLeaveRespVO>> leaveMap,
                                        Map<Long, List<AttendanceRateTraineeInfoVO>> attendanceMap,
                                        Map<Long, List<ClockInInfoStudentStatusVO>> lateMap,
                                        Map<Long, List<TraineeEvaluationDetailPageRespVO>> evaluationMap) {
        // 如果已有成绩，直接返回
        if (info.getScore() != null) {
            return info.getScore();
        }

        // 仅处理 acquisitionMode == 1 的情况 自动获取
        if (Objects.equals(info.getAcquisitionMode(), 1)) {
            int dataSource = info.getDataSource();
            Long traineeId = info.getTraineeId();

            // 处理请假相关数据
            if (isLeaveDataSource(dataSource)) {
                return getLeaveScore(traineeId, dataSource, leaveMap);
            }

            // 处理考勤率相关数据
            if (isAttendanceRateDataSource(dataSource)) {
                return getAttendanceRateScore(traineeId, dataSource, attendanceMap);
            }

            // 处理课程评价率
            if (Objects.equals(dataSource, DataSourceEnum.COURSE_EVALUATION_RATE.getCode())) {
                return getEvaluationScore(traineeId, evaluationMap);
            }

            // 处理迟到次数
            if (Objects.equals(dataSource, DataSourceEnum.LATE_COUNT.getCode())) {
                return getLateCountScore(traineeId, lateMap);
            }
        }

        return "0";
    }

    // 判断是否是请假数据来源
    private boolean isLeaveDataSource(int dataSource) {
        return (dataSource >= DataSourceEnum.PERSONAL_LEAVE_NUM.getCode() && dataSource <= DataSourceEnum.FIVE_CANS_LEAVE_NUM.getCode()) ||
                (dataSource >= DataSourceEnum.PERSONAL_LEAVE_DAYS.getCode() && dataSource <= DataSourceEnum.FIVE_CANS_LEAVE_DAYS.getCode());
    }

    // 计算请假相关数据
    private String getLeaveScore(Long traineeId, int dataSource, Map<Long, List<TraineeLeaveRespVO>> leaveMap) {
        List<TraineeLeaveRespVO> leaves = leaveMap.get(traineeId);
        if (CollUtil.isEmpty(leaves)) {
            return "0";
        }

        TraineeLeaveType leaveType = getLeaveTypeByDataSource(dataSource);
        if (leaveType == null) {
            return "0";
        }
        if (isLeaveCountDataSource(dataSource)) {
            return String.valueOf(leaves.stream()
                    .filter(leave -> Objects.equals(leave.getLeaveType(), leaveType.getCode()))
                    .count());
        } else {
            double totalDays = leaves.stream()
                    .filter(leave -> Objects.equals(leave.getLeaveType(), leaveType.getCode()))
                    .mapToDouble(TraineeLeaveRespVO::getDays)
                    .sum();
            return String.format("%.1f", totalDays);
        }
    }

    // 判断是否是请假次数来源
    private boolean isLeaveCountDataSource(int dataSource) {
        return dataSource == DataSourceEnum.PERSONAL_LEAVE_NUM.getCode() ||
                dataSource == DataSourceEnum.SICK_LEAVE_NUM.getCode() ||
                dataSource == DataSourceEnum.FIVE_CANS_LEAVE_NUM.getCode();
    }

    // 判断是否是请假天数来源
    private boolean isLeaveDaysDataSource(int dataSource) {
        return dataSource == DataSourceEnum.PERSONAL_LEAVE_DAYS.getCode() ||
                dataSource == DataSourceEnum.SICK_LEAVE_DAYS.getCode() ||
                dataSource == DataSourceEnum.FIVE_CANS_LEAVE_DAYS.getCode();
    }

    // 根据数据来源获取请假类型
    private TraineeLeaveType getLeaveTypeByDataSource(int dataSource) {
        if (dataSource == DataSourceEnum.PERSONAL_LEAVE_NUM.getCode() || dataSource == DataSourceEnum.PERSONAL_LEAVE_DAYS.getCode()) {
            return TraineeLeaveType.BUSINESS;
        } else if (dataSource == DataSourceEnum.SICK_LEAVE_NUM.getCode() || dataSource == DataSourceEnum.SICK_LEAVE_DAYS.getCode()) {
            return TraineeLeaveType.SICK;
        } else if (dataSource == DataSourceEnum.FIVE_CANS_LEAVE_NUM.getCode() || dataSource == DataSourceEnum.FIVE_CANS_LEAVE_DAYS.getCode()) {
            return TraineeLeaveType.FIVE_CANS;
        }
        return null;
    }

    // 判断是否是考勤率相关数据来源
    private boolean isAttendanceRateDataSource(int dataSource) {
        return dataSource >= DataSourceEnum.CLASS_ATTENDANCE_RATE.getCode() &&
                dataSource <= DataSourceEnum.ACCOMMODATION_RATE.getCode();
    }

    // 计算考勤率
    private String getAttendanceRateScore(Long traineeId, int dataSource, Map<Long, List<AttendanceRateTraineeInfoVO>> attendanceMap) {
        List<AttendanceRateTraineeInfoVO> rates = attendanceMap.get(traineeId);
        if (CollUtil.isEmpty(rates)) {
            return "0";
        }
        AttendanceRateTraineeInfoVO rateInfo = rates.get(0);

        if (Objects.equals(dataSource, DataSourceEnum.CLASS_ATTENDANCE_RATE.getCode())) {
            return rateInfo.getClassRate();
        } else if (Objects.equals(dataSource, DataSourceEnum.MEAL_ATTENDANCE_RATE.getCode())) {
            return rateInfo.getMealRate();
        } else if (Objects.equals(dataSource, DataSourceEnum.ACCOMMODATION_RATE.getCode())) {
            return rateInfo.getAccommodationRate();
        }
        return "0";
    }

    // 计算课程评价率
    private String getEvaluationScore(Long traineeId, Map<Long, List<TraineeEvaluationDetailPageRespVO>> evaluationMap) {
        List<TraineeEvaluationDetailPageRespVO> evaluations = evaluationMap.get(traineeId);
        if (CollUtil.isEmpty(evaluations)) {
            return "0";
        }
        return String.format("%.2f", evaluations.get(0).getRatio() * 100);
    }

    // 计算迟到次数
    private String getLateCountScore(Long traineeId, Map<Long, List<ClockInInfoStudentStatusVO>> lateMap) {
        List<ClockInInfoStudentStatusVO> lateInfo = lateMap.get(traineeId);
        return String.valueOf(CollUtil.isEmpty(lateInfo) ? 0 : lateInfo.size());
    }

    private TraineeLeavePageReqVO buildLeaveReqVO(Integer classId, int pageSize) {
        TraineeLeavePageReqVO reqVO = new TraineeLeavePageReqVO();
        reqVO.setClassId(Long.valueOf(classId));
        reqVO.setPageNo(1);
        reqVO.setStatus(LeaveTaskStatus.APPROVE.getCode());
        reqVO.setPageSize(pageSize);
        return reqVO;
    }

    private AttendanceRateReqVO buildAttendanceReqVO(Integer classId) {
        AttendanceRateReqVO reqVO = new AttendanceRateReqVO();
        reqVO.setClassId(Long.valueOf(classId));
        return reqVO;
    }

    private ClockInInfoPageReqVO buildLateReqVO(Integer classId) {
        ClockInInfoPageReqVO reqVO = new ClockInInfoPageReqVO();
        reqVO.setClassId(Long.valueOf(classId));
        reqVO.setPageNo(1);
        reqVO.setPageSize(10000);
        reqVO.setTraineeStatus(2);
        reqVO.setType(0);
        reqVO.setClockInType(0);
        return reqVO;
    }

    private TraineeEvaluationDetailPageReqVO buildEvaluationReqVO(Integer classId) {
        TraineeEvaluationDetailPageReqVO reqVO = new TraineeEvaluationDetailPageReqVO();
        reqVO.setClassId(Long.valueOf(classId));
        reqVO.setPageNo(1);
        reqVO.setPageSize(10000);

        return reqVO;
    }


//    @Override
//    public List<ClassCompletionInfoRespVO> getClassCompletionInfo(Integer classId) {
//
////        List<ClassCompletionDO> traineeInfoList = classCompletionMapper.listByClassId(classId);
//        List<ClassCompletionDO> traineeInfoList = classCompletionMapper.getInfoByClassId(classId);
//
//
//        Map<Long, List<ClassCompletionDO>> traineeInfoMap = traineeInfoList.stream()
//                .collect(Collectors.groupingBy(
//                        ClassCompletionDO::getTraineeId,
//                        LinkedHashMap::new, // 使用 LinkedHashMap 来保持顺序
//                        Collectors.toList()
//                ));
//
//        List<ClassCompletionInfoRespVO> respVOS = new ArrayList<>();
//        traineeInfoMap.forEach((traineeId, infoList) -> {
//            ClassCompletionInfoRespVO respVO =  new ClassCompletionInfoRespVO();
//            Map<String,String> map = new LinkedHashMap<>();
//            infoList.forEach(info->{
//                respVO.setId(info.getId());
//                respVO.setTraineeId(info.getTraineeId());
//                respVO.setTraineeName(info.getTraineeName());
//                respVO.setGroupName(info.getGroupName());
//                respVO.setUnitPosition(info.getUnitPosition());
//                respVO.setClassPosition(info.getClassPosition());
//
//                if (info.getAcquisitionMode() != null && info.getAcquisitionMode() == 1){
//                    //自动获取
//                    if (info.getDataSource() == 0 ||info.getDataSource() == 1 ||info.getDataSource() == 2 ){
//                        //请假
//                        TraineeLeavePageReqVO reqVO1 = new TraineeLeavePageReqVO();
//                        reqVO1.setClassId(Long.valueOf(classId));
//                        reqVO1.setPageNo(1);
//                        reqVO1.setPageSize(10000);
//
//                        List<TraineeLeaveRespVO> list = new ArrayList<>();
//                        if (info.getDataSource() == 0){
//                            reqVO1.setLeaveType(1);
//                            list = leaveService.pageList(reqVO1).getList();
//                        }else if (info.getDataSource() == 1){
//                            reqVO1.setLeaveType(2);
//                            list = leaveService.pageList(reqVO1).getList();
//                        }else if (info.getDataSource() == 2){
//                            reqVO1.setLeaveType(3);
//                            list = leaveService.pageList(reqVO1).getList();
//                        }
//                        Map<Long, List<TraineeLeaveRespVO>> leaveMap = list.stream().collect(Collectors.groupingBy(TraineeLeaveRespVO::getTraineeId));
//
//                        if (leaveMap.get(info.getTraineeId()) != null){
//                            map.put(info.getSerialNumber(),String.valueOf(leaveMap.get(info.getTraineeId()).size()));
//                        }else {
//                            map.put(info.getSerialNumber(),String.valueOf(0));
//                        }
//                    } else if (info.getDataSource() == 3 || info.getDataSource() == 4 || info.getDataSource() == 5) {
//
//                        //考勤三率
//                        AttendanceRateReqVO reqVO = new AttendanceRateReqVO();
//                        reqVO.setClassId(Long.valueOf(classId));
//                        Map<Long, List<AttendanceRateTraineeInfoVO>> attendanceMap = clockInInfoService.getListForAttendanceThreeRate(reqVO).getTraineeInfoList().stream()
//                                .collect(Collectors.groupingBy(AttendanceRateTraineeInfoVO::getTraineeId));
//
//                        List<AttendanceRateTraineeInfoVO> traineeRate = attendanceMap.get(info.getTraineeId());
//                        if(!traineeRate.isEmpty()){
//                            AttendanceRateTraineeInfoVO traineeInfo = traineeRate.get(0);
//
//                            if (info.getDataSource()==3){
//                                map.put(info.getSerialNumber(),traineeInfo.getClassRate());
//                            }
//
//                            if (info.getDataSource()==4){
//                                map.put(info.getSerialNumber(),traineeInfo.getMealRate());
//                            }
//
//                            if (info.getDataSource()==4){
//                                map.put(info.getSerialNumber(),traineeInfo.getAccommodationRate());
//                            }
//
//                        }else if (info.getDataSource() == 6){
//                            TraineeEvaluationDetailPageReqVO reqVO2 = new TraineeEvaluationDetailPageReqVO();
//                            reqVO2.setClassId(Long.valueOf(classId));
//                            List<TraineeEvaluationDetailPageRespVO> list = evaluationResponseService.getTraineeEvaluationDetailPage(reqVO2).getList();
//
//                            Map<Long, List<TraineeEvaluationDetailPageRespVO>> map1 = list.stream().collect(Collectors.groupingBy(TraineeEvaluationDetailPageRespVO::getTraineeId));
//                            List<TraineeEvaluationDetailPageRespVO> evaluationList = map1.get(info.getTraineeId());
//                            if (!evaluationList.isEmpty()){
//                                TraineeEvaluationDetailPageRespVO evaluation = evaluationList.get(0);
//                                map.put(info.getSerialNumber(),String.valueOf(evaluation.getRatio()));
//                            }else {
//                                map.put(info.getSerialNumber(),String.valueOf(0));
//                            }
//                        }else {
//                            map.put(info.getSerialNumber(),String.valueOf(0));
//                        }
//
//
//                    } else if (info.getDataSource() == 7) {
//                        AttendanceRateReqVO reqVO = new AttendanceRateReqVO();
//                        reqVO.setClassId(Long.valueOf(classId));
//                        List<TraineeAttendanceInfoDTO> attendanceInfoList = clockInInfoService.getLateInfo(reqVO);
//
//                        Map<Long, List<TraineeAttendanceInfoDTO>> lateMap = attendanceInfoList.stream().collect(Collectors.groupingBy(TraineeAttendanceInfoDTO::getTraineeId));
//                        if (lateMap.get(info.getTraineeId()) != null){
//                            map.put(info.getSerialNumber(),String.valueOf(lateMap.get(info.getTraineeId()).size()));
//                        }else {
//                            map.put(info.getSerialNumber(),String.valueOf(0));
//                        }
//                    }
////                    map.put(info.getSerialNumber(),info.getScore());
//                }else {
//                    map.put(info.getSerialNumber(),String.valueOf(info.getScore()));
//                }
//
//
//            });
//            respVO.setMapScore(map);
//
//            respVOS.add(respVO);
//        });
//
//        return respVOS;
//    }

    @Override
    public boolean saveInfo(List<SaveClassCompletionInfoReqVO> reqVOS) {
        if (reqVOS.isEmpty()) {
            return false;
        }

        // 查询现有的班级完成信息
        List<ClassCompletionDO> traineeInfoList = classCompletionMapper.listByClassId(reqVOS.get(0).getClassId());

        List<Long> traineeList = traineeInfoList.stream().map(ClassCompletionDO::getTraineeId).collect(Collectors.toList());


        // 将输入按 TraineeId 分组
        Map<Long, List<SaveClassCompletionInfoReqVO>> groupedMap = reqVOS.stream()
                .collect(Collectors.groupingBy(SaveClassCompletionInfoReqVO::getTraineeId));

        Map<Long, List<ClassCompletionDO>> traineeMap = traineeInfoList.stream()
                .collect(Collectors.groupingBy(ClassCompletionDO::getTraineeId));

        // 按学员是否已存在拆分为更新和新增的列表
        List<ClassCompletionDO> updateList = new ArrayList<>();
        List<ClassCompletionDO> insertList = new ArrayList<>();

        // 遍历输入数据，区分更新和新增
        for (SaveClassCompletionInfoReqVO reqVO : reqVOS) {

            Map<String, String> mapScore = reqVO.getMapScore();
            if (traineeList.contains(reqVO.getTraineeId())){

                List<ClassCompletionDO> list = traineeMap.get(reqVO.getTraineeId());

                for (ClassCompletionDO classCompletionDO : list) {
                    classCompletionDO.setScore(mapScore.get(classCompletionDO.getSerialNumber()));
                    updateList.add(classCompletionDO);
                }
//
//
//                Map<Long, ClassCompletionDO> map = serialMap.get(key).stream().collect(Collectors.toMap(ClassCompletionDO::getTraineeId, Function.identity()));
//
//
//                ClassCompletionDO classCompletionDO = serialMap.get(key).get(0);
//                classCompletionDO.setScore(reqVO.getMapScore().get(key));
//                updateList.add(classCompletionDO);
            }else {

                mapScore.forEach((key, value) -> {
                    ClassCompletionDO classCompletionDO = new ClassCompletionDO();
                    classCompletionDO.setTraineeId(reqVO.getTraineeId());
                    classCompletionDO.setTraineeName(reqVO.getTraineeName());
                    classCompletionDO.setGroupName(reqVO.getGroupName());
                    classCompletionDO.setUnitPosition(reqVO.getUnitPosition());
                    classCompletionDO.setClassPosition(reqVO.getClassPosition());
                    classCompletionDO.setScore(mapScore.get(key));
                    classCompletionDO.setClassId(reqVO.getClassId());
                    classCompletionDO.setSerialNumber(key);
                    insertList.add(classCompletionDO);

                });

            }

        }

        if (!updateList.isEmpty()) {
            // 批量更新
            classCompletionMapper.updateBatch(updateList);
        }

        if (!insertList.isEmpty()) {
            // 批量插入
            classCompletionMapper.insertBatch(insertList);
        }

        return true;
    }


//    public static void main(String[] args) {
//
//        List<CompletionTemplateCreateReqVO> list = parseJsonToList(CompletionTemplateConstant.json);
//        System.out.println(list);
//    }
    public List<CompletionTemplateCreateReqVO> parseJsonToList(String json,Integer campus) {
        ObjectMapper objectMapper = new ObjectMapper();
        List<CompletionTemplateCreateReqVO> list = new ArrayList<>();

        try {
            JsonNode jsonArray = objectMapper.readTree(json);
            for (JsonNode node : jsonArray) {
                CompletionTemplateCreateReqVO vo = new CompletionTemplateCreateReqVO();
                vo.setSerialNumber(node.path("serialNumber").asText(null));
                vo.setColumnName(node.path("columnName").asText(null));
                vo.setConversionAnnouncement(node.path("conversionAnnouncement").asText(null));

                // 处理 maxScore
                String maxScoreStr = node.path("maxScore").asText(null);
                vo.setMaxScore(maxScoreStr == null || maxScoreStr.isEmpty() ? null : Integer.parseInt(maxScoreStr));

                // 处理 initialScore
                String initialScoreStr = node.path("initialScore").asText(null);
                vo.setInitialScore(initialScoreStr == null || initialScoreStr.isEmpty() ? null : Integer.parseInt(initialScoreStr));

//                vo.setAcquisitionMode(node.path("acquisitionMode").isNull() ? null : node.path("acquisitionMode").asInt());
                String acquisitionModeStr = node.path("acquisitionMode").asText(null);
                vo.setAcquisitionMode(acquisitionModeStr == null || acquisitionModeStr.isEmpty() ? null : Integer.parseInt(acquisitionModeStr));
//                vo.setAcquisitionMode(node.path("acquisitionMode").isNull() ? null : node.path("acquisitionMode").asInt());

                String dataSourceStr = node.path("dataSource").asText(null);
                vo.setDataSource(dataSourceStr == null || dataSourceStr.isEmpty() ? null : Integer.parseInt(dataSourceStr));
//                vo.setDataSource(node.path("dataSource").isNull() ? null : node.path("dataSource").asInt());


                vo.setAssessmentName(node.path("assessmentName").asText(null));
                vo.setCampus(campus);
                vo.setDefaultRule(node.path("defaultRule").isNull() ? null : node.path("defaultRule").asInt());
                vo.setTemplateName(node.path("templateName").asText(null));
                vo.setModuleName(node.path("moduleName").asText(null));
                vo.setIdCode(node.path("idCode").asText(null));
                vo.setBuiltinTemplate(1);

                list.add(vo);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }


        return list;
    }
    @Override
    public Boolean generationTemplate() {


        Long campus = completionTemplateMapper.getMainCampus(getTenantId());
//        Integer campus = dictData != null ? dictData.getId().intValue() : null;

        if (Objects.isNull(campus)){
            return true;
        }
        //查该租户下的内置模板
        Long count = completionTemplateMapper.getBuildInTemplate(campus.intValue());

        // 判断改租户下是否存在内置模板，如果存在直接返回
        if (count > 0) {
            return true;
        }

        // 不存在则创建内置模板
        try {
            List<CompletionTemplateCreateReqVO> list = parseJsonToList(CompletionTemplateConstant.JSON_DATA,campus.intValue());
            this.addCompletionTemplate(list);
        } catch (Exception e) {
            log.info("内置模板生成失败：{}",e.getMessage());
        }
        return true;

    }


    @Override
    public SchoolFeedbackFormRespVO schoolFeedbackForm(Long classId) {
        SchoolFeedbackFormRespVO respVO = new SchoolFeedbackFormRespVO();

        // 根据班级id获取班级信息
        ClassManagementDO classManagementDO = classManagementMapper.selectById(classId);
        if (Objects.isNull(classManagementDO)) {
            return respVO;
        }

        // 班级信息赋值
        classInformationAssignment(classManagementDO, respVO);

        // 获取班级所有学员
        List<TraineeDO> traineeList = traineeService.getAllTraineeByClassIds(Collections.singletonList(classId));
        List<SchoolFeedbackFormRespVO.TraineeInfoVO> traineeInfoList = ClassCompletionConvert.INSTANCE.convertTraineeInfoList(traineeList);

        // 获取考核相关数据
        String idCode = getIdCode(classManagementDO);

        // 获取成绩数据
        List<TraineeScoreVO> scoreList = classCompletionMapper.getScoreList(classId, idCode);

        // 创建成绩映射
        List<String> assessmentName = getAssessmentNames(scoreList);

        // 获取请假数据并处理
        List<TraineeLeaveRespVO> leaveList = getTraineeLeaveList(classId);

        // 处理请假天数分组
        Map<Long, Map<Integer, Float>> leaveDaysMap = processLeaveDays(leaveList);

        // 创建各类考核项映射
        Map<Long, Float> businessMap = createBusinessMap(assessmentName, leaveDaysMap, scoreList);
        Map<Long, Float> sickMap = createSickMap(assessmentName, leaveDaysMap, scoreList);
        Map<Long, Float> fiveCansMap = createFiveCansMap(assessmentName, leaveDaysMap, scoreList);
        Map<Long, Float> lateAndLeaveEarlyMap = createLateEarlyMap(scoreList);
        Map<Long, Float> absentClassMap = createAbsentClassMap(scoreList);

        // 处理考勤数据
        Map<Long, AttendanceRateTraineeInfoVO> attendanceMap = processAttendanceData(classManagementDO, assessmentName, scoreList);

        // 学员信息赋值
        traineeInformationAssignment(traineeInfoList, attendanceMap, businessMap, sickMap, fiveCansMap,lateAndLeaveEarlyMap, absentClassMap);

        respVO.setTraineeInfoList(traineeInfoList);
        return respVO;
    }

    /**
     * 获取学员请假列表
     * @param classId 班级ID
     * @return 请假列表
     */
    private List<TraineeLeaveRespVO> getTraineeLeaveList(Long classId) {
        TraineeLeavePageReqVO leaveReqVO = new TraineeLeavePageReqVO();
        leaveReqVO.setClassId(classId);
        leaveReqVO.setPageNo(1);
        leaveReqVO.setPageSize(Integer.MAX_VALUE);
        return leaveService.pageList(leaveReqVO).getList();
    }

    /**
     * 获取序列号列表
     * @param completionDOList 结业模板列表
     * @return 序列号列表
     */
    private List<String> getSerialNumbers(List<CompletionTemplateDO> completionDOList) {
        return completionDOList.stream()
                .map(CompletionTemplateDO::getSerialNumber)
                .collect(Collectors.toList());
    }

    /**
     * 创建成绩映射
     * @param scores 原始成绩数据
     * @return 成绩映射表
     */
    private Map<String, String> createScoreMapping(List<Map<String, String>> scores) {
        Map<String, String> scoreMap = new HashMap<>();
        for (Map<String, String> score : scores) {
            String serialNumber = score.get("assessment_name");
            String scoreValue = String.valueOf(score.get("score"));
            if (serialNumber != null && scoreValue != null) {
                scoreMap.put(serialNumber, scoreValue);
            }
        }
        return scoreMap;
    }

    /**
     * 获取考核名称列表
     * @param scoreList 成绩列表
     * @return 去重后的考核名称列表
     */
    private List<String> getAssessmentNames(List<TraineeScoreVO> scoreList) {
        return scoreList.stream()
                .map(TraineeScoreVO::getAssessmentName)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 处理请假天数分组
     * @param leaveList 请假列表
     * @return 按学员和请假类型分组的请假天数
     */
    private Map<Long, Map<Integer, Float>> processLeaveDays(List<TraineeLeaveRespVO> leaveList) {
        return leaveList.stream()
                .collect(Collectors.groupingBy(
                        TraineeLeaveRespVO::getTraineeId,
                        Collectors.groupingBy(
                                TraineeLeaveRespVO::getLeaveType,
                                Collectors.summingDouble(TraineeLeaveRespVO::getDays)
                        )
                )).entrySet().stream().collect(Collectors.toMap(
                        Map.Entry::getKey,
                        e -> e.getValue().entrySet().stream().collect(Collectors.toMap(
                                Map.Entry::getKey,
                                entry -> entry.getValue().floatValue()
                        ))
                ));
    }

    /**
     * 创建事假扣分映射
     */
    private Map<Long, Float> createBusinessMap(List<String> assessmentName,
                                               Map<Long, Map<Integer, Float>> leaveDaysMap,
                                               List<TraineeScoreVO> scoreList) {
        return createLeaveOrScoreMap(assessmentName, leaveDaysMap, scoreList,
                TraineeLeaveType.BUSINESS.getCode(), CompletionTemplateConstant.BUSINESS_DEDUCTION);
    }

    /**
     * 创建病假扣分映射
     */
    private Map<Long, Float> createSickMap(List<String> assessmentName,
                                           Map<Long, Map<Integer, Float>> leaveDaysMap,
                                           List<TraineeScoreVO> scoreList) {
        return createLeaveOrScoreMap(assessmentName, leaveDaysMap, scoreList,
                TraineeLeaveType.SICK.getCode(), CompletionTemplateConstant.SICK_DEDUCTION);
    }

    /**
     * 创建五会假映射
     */
    private Map<Long, Float> createFiveCansMap(List<String> assessmentName,
                                               Map<Long, Map<Integer, Float>> leaveDaysMap,
                                               List<TraineeScoreVO> scoreList) {
        return createLeaveOrScoreMap(assessmentName, leaveDaysMap, scoreList,
                TraineeLeaveType.FIVE_CANS.getCode(), CompletionTemplateConstant.FIVE_CANS_LEAVE);
    }

    /**
     * 通用方法：创建请假类型或考核分数映射
     */
    private Map<Long, Float> createLeaveOrScoreMap(List<String> assessmentName,
                                                   Map<Long, Map<Integer, Float>> leaveDaysMap,
                                                   List<TraineeScoreVO> scoreList,
                                                   int leaveTypeCode,
                                                   String assessmentKey) {
        if (!assessmentName.contains(assessmentKey)) {
            //如果不包含则从请假管理查数据
            return leaveDaysMap.entrySet().stream()
                    .filter(e -> e.getValue().containsKey(leaveTypeCode))
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            e -> e.getValue().get(leaveTypeCode)
                    ));
        }

        //否则则从结业考核中取数据
        return scoreList.stream()
                .filter(o -> Objects.equals(o.getAssessmentName(), assessmentKey))
                .collect(Collectors.toMap(
                        TraineeScoreVO::getTraineeId,
                        TraineeScoreVO::getScore
                ));
    }

    /**
     * 创建迟到早退映射
     */
    private Map<Long, Float> createLateEarlyMap(List<TraineeScoreVO> scoreList) {
        return createSimpleScoreMap(scoreList, CompletionTemplateConstant.LATE_EARLY_DEDUCTION);
    }

    /**
     * 创建旷课映射
     */
    private Map<Long, Float> createAbsentClassMap(List<TraineeScoreVO> scoreList) {
        return createSimpleScoreMap(scoreList, CompletionTemplateConstant.ABSENT_CLASS);
    }

    /**
     * 通用方法：创建简单分数映射
     */
    private Map<Long, Float> createSimpleScoreMap(List<TraineeScoreVO> scoreList, String assessmentName) {
        return scoreList.stream()
                .filter(o -> Objects.equals(o.getAssessmentName(), assessmentName))
                .collect(Collectors.toMap(
                        TraineeScoreVO::getTraineeId,
                        TraineeScoreVO::getScore
                ));
    }

    /**
     * 处理考勤数据
     */
    private Map<Long, AttendanceRateTraineeInfoVO> processAttendanceData(ClassManagementDO classManagementDO,
                                                                         List<String> assessmentName,
                                                                         List<TraineeScoreVO> scoreList) {
        AttendanceRateReqVO reqVO = new AttendanceRateReqVO();
        reqVO.setClassId(classManagementDO.getId());
        reqVO.setStartTime(classManagementDO.getClassOpenTime().toLocalDate());
        reqVO.setEndTime(classManagementDO.getCompletionTime().toLocalDate());

        //考勤管理中考勤数据
        List<AttendanceRateTraineeInfoVO> attendanceList =
                clockInInfoService.getListForAttendanceThreeRate(reqVO).getTraineeInfoList();

        //根据学员id将考勤数据转Map
        Map<Long, AttendanceRateTraineeInfoVO> attendanceMap = attendanceList.stream()
                .collect(Collectors.toMap(
                        AttendanceRateTraineeInfoVO::getTraineeId,
                        Function.identity(),
                        (a, b) -> a
                ));

        // 定义考核映射关系
        Map<String, String> assessmentMapping = new HashMap<>();

        assessmentMapping.put(CompletionTemplateConstant.ACCOMMODATION_RATE, "setAccommodationRate");

        assessmentMapping.put(CompletionTemplateConstant.MEAL_RATE, "setMealRate");
        assessmentMapping.put(CompletionTemplateConstant.CLASS_RATE, "setClassRate");

        System.out.println(scoreList);
        // 更新考勤数据
        assessmentMapping.forEach((key, method) -> {
            //如果结业考核中存在则从结业考核中取数据
            if (assessmentName.contains(key)) {
                scoreList.stream()
                        .filter(o -> Objects.equals(o.getAssessmentName(), key))
                        .forEach(item -> attendanceMap
                                .computeIfAbsent(item.getTraineeId(), k -> new AttendanceRateTraineeInfoVO())
                                .setRate(method, String.valueOf(item.getScore())));
            }
        });

        return attendanceMap;
    }


    // 获取 completionDOList 的方法
    private List<CompletionTemplateDO> getCompletionDOList(ClassManagementDO classManagementDO,String idCode) {
        List<CompletionTemplateDO> completionDOList = new ArrayList<>();

        if (StrUtil.isNotBlank(idCode)) {
            completionDOList = completionTemplateMapper.getDefaultTemplateByIdCode(idCode, DefaultCompletionEnum.NOT_DEFAULT.getCode());
        }

        // 如果 completionDOList 为空，查看校区默认模板
        if (CollUtil.isEmpty(completionDOList)) {
            completionDOList = completionTemplateMapper.getDefaultTemplateByIdCode(idCode, DefaultCompletionEnum.DEFAULT.getCode());
        }

        return completionDOList;
    }

    // 获取 idCode 的方法
    private String getIdCode(ClassManagementDO classManagementDO) {
        String idCode = classManagementDO.getIdCode();

        if (StrUtil.isBlank(idCode)) {
            idCode = completionTemplateMapper.getDefaultTemplateByCampus(classManagementDO.getCampus());
        }

        return idCode;
    }

    // 学员信息赋值
    private void traineeInformationAssignment( List<SchoolFeedbackFormRespVO.TraineeInfoVO> traineeINfoList,
                                               Map<Long, AttendanceRateTraineeInfoVO> attendanceMap,
                                               Map<Long, Float> businessMap,
                                               Map<Long, Float> sickMap,
                                               Map<Long, Float> fiveCansMap,
                                               Map<Long, Float> lateAndLeaveEarlyMap,
                                               Map<Long, Float> absentClassMap) {

        for (SchoolFeedbackFormRespVO.TraineeInfoVO vo : traineeINfoList) {

            //请假
            vo.setBusiness(businessMap.getOrDefault(vo.getId(),0.0f));
            vo.setSick(sickMap.getOrDefault(vo.getId(),0.0f));
            vo.setFiveCans(fiveCansMap.getOrDefault(vo.getId(),0.0f));
            vo.setLateAndLeaveEarly(lateAndLeaveEarlyMap.getOrDefault(vo.getId(),0f));
            vo.setAbsentClass(absentClassMap.getOrDefault(vo.getId(),0.0f));

            //考勤
            vo.setClassRate(attendanceMap.getOrDefault(vo.getId(),new AttendanceRateTraineeInfoVO()).getClassRate());
            vo.setMealRate(attendanceMap.getOrDefault(vo.getId(),new AttendanceRateTraineeInfoVO()).getMealRate());
            vo.setAccommodationRate(attendanceMap.getOrDefault(vo.getId(),new AttendanceRateTraineeInfoVO()).getAccommodationRate());
            vo.setSelfLearnRate("100.00");

            //班委
            vo.setSchoolPosition(attendanceMap.getOrDefault(vo.getId(),new AttendanceRateTraineeInfoVO()).getClassCommitteeName());

            //结业考核成绩
//            vo.setLearningExperience(scoreMap.getOrDefault("学习心得 (原分)",""));
//            vo.setTheoreticalGrades(scoreMap.getOrDefault("考试成绩 (原分)",""));
//            vo.setGraduationThesisScore(scoreMap.getOrDefault("结业论文成绩 (原分)",""));
//            vo.setComprehensiveEvaluationScore(scoreMap.getOrDefault("量化得分",""));

        }



    }

    /**
     * 班级信息赋值
     * @param classManagementDO 班级DO
     * @param respVO 返回VO类
     */
    private void classInformationAssignment(ClassManagementDO classManagementDO, SchoolFeedbackFormRespVO respVO) {
        LocalDateTime classOpenTime = classManagementDO.getClassOpenTime();
        LocalDateTime completionTime = classManagementDO.getCompletionTime();

        String time = classOpenTime.toLocalDate().toString() + "-" + completionTime.toLocalDate().toString();
        respVO.setClassName(classManagementDO.getClassName());
        respVO.setTrainingTime(time);
        respVO.setClassTypeDictId(classManagementDO.getClassTypeDictId());

        if (Objects.nonNull(classManagementDO.getClassTeacherLead())){
            Long classTeacherLead = classManagementDO.getClassTeacherLead();
            TeacherInformationDO teacherInformationDO = teacherInformationMapper.selectById(classTeacherLead);
            respVO.setClassTeacherName(teacherInformationDO.getName());
        }
    }


    private void assignScores(Map<Long, List<SaveClassCompletionInfoReqVO>> groupedMap, List<ClassCompletionDO> targetList) {
        for (ClassCompletionDO item : targetList) {
            List<SaveClassCompletionInfoReqVO> reqList = groupedMap.get(item.getTraineeId());
            if (reqList != null && !reqList.isEmpty()) {
                Map<String, String> mapScore = reqList.get(0).getMapScore();
                if (mapScore != null && mapScore.containsKey(item.getSerialNumber())) {
                    item.setScore(mapScore.get(item.getSerialNumber()));
                }
            }
        }
    }

    // 新增时确保设置 serialNumber 和 score
    private void setSerialNumbersAndScores(List<ClassCompletionDO> insertList, List<SaveClassCompletionInfoReqVO> reqVOS) {
        for (ClassCompletionDO completionDO : insertList) {
            // 查找对应的 reqVO
            SaveClassCompletionInfoReqVO reqVO = reqVOS.stream()
                    .filter(vo -> vo.getTraineeId().equals(completionDO.getTraineeId()))
                    .findFirst()
                    .orElse(null);

            if (reqVO != null) {
                // 获取 serialNumber 和 score
                Map<String, String> mapScore = reqVO.getMapScore();
                if (mapScore != null && !mapScore.isEmpty()) {
                    for (String serialNumber : mapScore.keySet()) {
                        completionDO.setSerialNumber(serialNumber);  // 设置 serialNumber
                        completionDO.setScore(mapScore.get(serialNumber));  // 设置对应的 score
                        break;  // 如果一个 trainee 对应多个 serialNumber，根据实际业务调整
                    }
                }
            }
        }
    }


    /**
     * 唯一性校验
     * 模版名称唯一性校验
     *  @param id
     */
    public void campusNameRules(Integer id, String templateName){

        //在数据库中 匹配数据
        List<CompletionTemplateDO> completionTemplateDO = completionTemplateMapper.selectByTemplateName(templateName);

        if(completionTemplateDO.isEmpty()){
            return ;
        }

        if(id == null){
            throw exception(COMPLETION_TEMPLATE_NAME_EXISTS);
        }

        if(!completionTemplateDO.get(0).getId().equals(id)){
            throw exception(COMPLETION_TEMPLATE_NAME_EXISTS);
        }
    }

    /**
     * 唯一性校验
     * 规则名称
     * 同一个校区默认规则唯一
     *  @param id
     *  @param defaultRule
     *  @param campus
     */
    public void campusDefaultRules(Integer id, Integer defaultRule, Integer campus){

        if(defaultRule == 1){
            return;
        }

        //在数据库中 匹配数据
        List<CompletionTemplateDO> completionTemplateDO = completionTemplateMapper.selectByDefaultRule(defaultRule, campus);

        if(completionTemplateDO.isEmpty()){
            return ;
        }

        if(id == null){
            throw exception(COMPLETION_TEMPLATE_DEFAULT_RULE_EXISTS);
        }

        if(!completionTemplateDO.get(0).getId().equals(id)){
            throw exception(COMPLETION_TEMPLATE_DEFAULT_RULE_EXISTS);
        }
    }

    /**
     * 唯一性校验
     * 模版名称唯一性校验
     *  @param idCode
     *  @param templateName
     */
    public void editNameRules(String idCode, String templateName){

        //在数据库中 匹配数据
        List<CompletionTemplateDO> completionTemplateDO = completionTemplateMapper.selectByTemplateNameEdit(idCode, templateName);

        if(completionTemplateDO.isEmpty()){
            return;
        }else{
            throw exception(COMPLETION_TEMPLATE_NAME_EXISTS);
        }
    }

    /**
     * 唯一性校验
     * 规则名称
     * 同一个校区默认规则唯一
     *  @param defaultRule
     *  @param campus
     */
    public void editDefaultRules(Integer defaultRule, Integer campus, String idCode){

        if(defaultRule == 1){
            return;
        }


        //在数据库中 匹配数据
        List<CompletionTemplateDO> completionTemplateDO = completionTemplateMapper.selectByDefaultRuleEdit(defaultRule, campus, idCode);

        if(completionTemplateDO.isEmpty()){
            return;
        }else{
            throw exception(COMPLETION_TEMPLATE_DEFAULT_RULE_EXISTS);
        }

    }


    // 从数据库查询动态表头
    public  List<Map<String, Map<String, List<String>>>> getDynamicHeaders(Integer classId,List<ClassCompletionTemplateDO> list) {
        List<Map<String, Map<String, List<String>>>> headers;

//        List<CompletionTemplateDO> list = getCompletionTemplateList(classManagementMapper.selectById(classId));


        headers = convertToHeaders(list);

//        // 构建理论学习表头
//        Map<String, Map<String, List<String>>> theoryStudy = new HashMap<>();
//        theoryStudy.put("理论学习50分", new HashMap<String, List<String>>() {{
//            put("学习考勤20", Arrays.asList("考试成绩"));
//            put("学习评价", Arrays.asList("到课率", "学习笔记"));
//        }});
//
//        // 构建党性锻炼表头
//        Map<String, Map<String, List<String>>> partyStudy = new HashMap<>();
//        partyStudy.put("党性锻炼50分", new HashMap<String, List<String>>() {{
//            put("考勤纪律", Arrays.asList("考勤记录", "组织纪律"));
//            put("廉洁纪律", Arrays.asList("廉洁行为", "道德规范"));
//        }});
//
//        // 构建加分项表头
//        Map<String, Map<String, List<String>>> bonusItems = new HashMap<>();
//        bonusItems.put("加分项", new HashMap<String, List<String>>() {{
//            put("奖励记录", Arrays.asList("发表文章", "竞赛获奖"));
//        }});
//
//        // 构建另一个加分项表头
//        Map<String, Map<String, List<String>>> bonusItems1 = new HashMap<>();
//        bonusItems1.put("加分项1", new HashMap<String, List<String>>() {{
//            put("奖励记录1", Arrays.asList("发表文章1", "竞赛获奖1"));
//        }});
//
//        // 将所有动态表头加入列表
//        headers.add(theoryStudy);
//        headers.add(partyStudy);
//        headers.add(bonusItems);
//        headers.add(bonusItems1);

        return headers;
    }

    private List<ClassCompletionTemplateDO> getCompletionTemplateList(ClassManagementDO classDO) {
        List<ClassCompletionTemplateDO> list;

        if (StrUtil.isNotBlank(classDO.getIdCode())){
            list = classCompletionTemplateMapper.selectByTemplateCode(classDO.getId(), classDO.getIdCode());
        }else {
            //根据班级id查询对应的表头内容
            list = completionTemplateMapper.selectClassDefaultRule(0, classDO.getCampus());
        }
        return list;
    }


    public static List<Map<String, Map<String, List<String>>>> convertToHeaders(List<ClassCompletionTemplateDO> list) {
        list.removeIf(item -> item.getSerialNumber().contains("S"));

        // 使用 Stream API 进行分组和转换
        return list.stream()
                .collect(Collectors.groupingBy(
                        ClassCompletionTemplateDO::getModuleName,
                        LinkedHashMap::new, // 保证 moduleName 的顺序
                        Collectors.toList()
                ))
                .entrySet().stream()
                .map(entry -> {
                    String moduleName = entry.getKey();

                    // 使用 LinkedHashMap 保证 columnName 的顺序
                    Map<String, List<String>> columnMap = new LinkedHashMap<>();
                    entry.getValue().forEach(trainee -> {
                        columnMap.computeIfAbsent(trainee.getColumnName(), k -> new ArrayList<>())
                                .add(trainee.getAssessmentName());
                    });

                    // 封装结果
                    Map<String, Map<String, List<String>>> moduleMap = new LinkedHashMap<>();
                    moduleMap.put(moduleName, columnMap);
                    return moduleMap;
                })
                .collect(Collectors.toList());

    }


    // 从数据库查询学生信息
//    public List<Map<String, Object>> getStudentData(Integer classId) {
//
//
//        // 查出班级学员信息
//        List<ClassCompletionDO> infoList = classCompletionMapper.getInfoByClassId(classId);
//
//
//        for (ClassCompletionDO info : infoList) {
//
//
//            if (info.getAcquisitionMode() != null && info.getAcquisitionMode() == 1){
//                //自动获取
//                if (info.getDataSource() == 0 ||info.getDataSource() == 1 ||info.getDataSource() == 2 ){
//                    //请假
//                    TraineeLeavePageReqVO reqVO1 = new TraineeLeavePageReqVO();
//                    reqVO1.setClassId(Long.valueOf(classId));
//                    reqVO1.setPageNo(1);
//                    reqVO1.setPageSize(10000);
//
//                    List<TraineeLeaveRespVO> list = new ArrayList<>();
//                    if (info.getDataSource() == 0){
//                        reqVO1.setLeaveType(1);
//                        list = leaveService.pageList(reqVO1).getList();
//                    }else if (info.getDataSource() == 1){
//                        reqVO1.setLeaveType(2);
//                        list = leaveService.pageList(reqVO1).getList();
//                    }else if (info.getDataSource() == 2){
//                        reqVO1.setLeaveType(3);
//                        list = leaveService.pageList(reqVO1).getList();
//                    }
//                    Map<Long, List<TraineeLeaveRespVO>> leaveMap = list.stream().collect(Collectors.groupingBy(TraineeLeaveRespVO::getTraineeId));
//
//                    if (leaveMap.get(info.getTraineeId()) != null){
//                        info.setScore(String.valueOf(leaveMap.get(info.getTraineeId()).size()));
////                        map.put(info.getSerialNumber(),String.valueOf(leaveMap.get(info.getTraineeId()).size()));
//                    }else {
//                        info.setScore(String.valueOf(0));
////                        map.put(info.getSerialNumber(),String.valueOf(0));
//                    }
//                } else if (info.getDataSource() == 3 || info.getDataSource() == 4 || info.getDataSource() == 5 || info.getDataSource() == 6) {
//
//                    //考勤三率
//                    AttendanceRateReqVO reqVO = new AttendanceRateReqVO();
//                    reqVO.setClassId(Long.valueOf(classId));
//                    Map<Long, List<AttendanceRateTraineeInfoVO>> attendanceMap = clockInInfoService.getListForAttendanceThreeRate(reqVO).getTraineeInfoList().stream()
//                            .collect(Collectors.groupingBy(AttendanceRateTraineeInfoVO::getTraineeId));
//
//                    List<AttendanceRateTraineeInfoVO> traineeRate = attendanceMap.get(info.getTraineeId());
//                    if(!traineeRate.isEmpty()){
//                        AttendanceRateTraineeInfoVO traineeInfo = traineeRate.get(0);
//
//                        if (info.getDataSource()==3){
//                            info.setScore(traineeInfo.getClassRate());
////                            map.put(info.getSerialNumber(),traineeInfo.getClassRate());
//                        }
//
//                        if (info.getDataSource()==4){
//                            info.setScore(String.valueOf(traineeInfo.getMealRate()));
////                            map.put(info.getSerialNumber(),traineeInfo.getMealRate());
//                        }
//
//                        if (info.getDataSource()==4){
//                            info.setScore(traineeInfo.getAccommodationRate());
////                            map.put(info.getSerialNumber(),traineeInfo.getAccommodationRate());
//                        }
//
//                    }else {
//                        info.setScore(String.valueOf(0));
////                        map.put(info.getSerialNumber(),String.valueOf(0));
//                    }
//
//
//                } else if (info.getDataSource() == 7) {
//                    AttendanceRateReqVO reqVO = new AttendanceRateReqVO();
//                    reqVO.setClassId(Long.valueOf(classId));
//                    List<TraineeAttendanceInfoDTO> attendanceInfoList = clockInInfoService.getLateInfo(reqVO);
//
//                    Map<Long, List<TraineeAttendanceInfoDTO>> lateMap = attendanceInfoList.stream().collect(Collectors.groupingBy(TraineeAttendanceInfoDTO::getTraineeId));
//                    if (lateMap.get(info.getTraineeId()) != null){
//                        info.setScore(String.valueOf(lateMap.get(info.getTraineeId()).size()));
////                        map.put(info.getSerialNumber(),String.valueOf(lateMap.get(info.getTraineeId()).size()));
//                    }else {
//                        info.setScore(String.valueOf(0));
////                        map.put(info.getSerialNumber(),String.valueOf(0));
//                    }
//                }
////                    map.put(info.getSerialNumber(),info.getScore());
//            }else {
////                info.setScore(String.valueOf(leaveMap.get(info.getTraineeId()).size()));
////                map.put(info.getSerialNumber(),String.valueOf(info.getScore()));
//            }
//
//
//
//        }
//
//
//        infoList.forEach(item->{
//            if (item.getScore() == null){
//                item.setScore(String.valueOf(0));
//            }
//        });
//
//        // 按 traineeId 分组，将 infoList 的数据分类到每个学员
//        Map<Long, List<ClassCompletionDO>> groupedByTraineeId = infoList.stream()
//                .collect(Collectors.groupingBy(
//                        ClassCompletionDO::getTraineeId,
//                        LinkedHashMap::new,
//                        Collectors.toList()
//                ));
//
//        List<Map<String, Object>> students = new ArrayList<>();
//
//        // 遍历分组后的数据
//        for (Map.Entry<Long, List<ClassCompletionDO>> entry : groupedByTraineeId.entrySet()) {
//            Long traineeId = entry.getKey();
//            List<ClassCompletionDO> records = entry.getValue();
//
//            // 只取学员的第一条记录用于存储基本信息（因为基本信息重复）
//            ClassCompletionDO firstRecord = records.get(0);
//
//            Map<String, Object> student = new HashMap<>();
//
//            // 设置基本信息
//            student.put("traineeId",firstRecord.getTraineeId().toString());
//            student.put("name", firstRecord.getTraineeName());
//            student.put("unitPosition", firstRecord.getUnitPosition());
//            student.put("classPosition", firstRecord.getClassPosition());
//            student.put("groupName", firstRecord.getGroupName());
//
//            // 提取该学员的所有成绩
//            List<String> scores = records.stream()
//                    .map(ClassCompletionDO::getScore)
//                    .collect(Collectors.toList());
//
//            student.put("scores", scores);
//
//            // 添加到结果列表
//            students.add(student);
//        }
//
//
//
//
//
//
////        for (int i = 1; i <= 5; i++) {
////            Map<String, Object> student = new HashMap<>();
////            student.put("studentName", "邓文娟" + i);
////            student.put("position", "研究生班干部");
////            student.put("committee", "-");
////            student.put("scores", Arrays.asList(1.0, 2.0)); // 对应动态表头的分数
////            students.add(student);
////        }
//        return students;
//    }

    public List<Map<String, Object>> getStudentData(HttpServletRequest request,Integer classId) {
        // 查出班级学员信息
        ClassManagementDO classDO = classManagementMapper.selectById(classId);

        List<ClassCompletionDO> infoList1;
        if (StrUtil.isNotBlank(classDO.getIdCode())){
            infoList1 = classCompletionMapper.getInfoByClassId(classId);
        }else {
            infoList1 = classCompletionMapper.getDefaultRuleInfoByClassId(classDO);
        }

        List<ClassCompletionDO> infoList = sortByModuleAndColumn(infoList1);
        // 预先查询数据
        // 批量获取数据
        List<TraineeLeaveRespVO> allLeaveList = leaveService.pageList(buildLeaveReqVO(classId, 10000)).getList();
        Map<Long, List<TraineeLeaveRespVO>> leaveMap = allLeaveList.stream()
                .collect(Collectors.groupingBy(TraineeLeaveRespVO::getTraineeId));

        List<AttendanceRateTraineeInfoVO> allAttendanceRates = clockInInfoService
                .getListForAttendanceThreeRate(buildAttendanceReqVO(classId))
                .getTraineeInfoList();
        Map<Long, List<AttendanceRateTraineeInfoVO>> attendanceMap = allAttendanceRates.stream()
                .collect(Collectors.groupingBy(AttendanceRateTraineeInfoVO::getTraineeId));

//        List<ClockInInfoStudentStatusVO> allAttendanceRates = clockInInfoService.getStudentClockingStatusPage(buildLateReqVO(classId)).getList();


//        Map<Long, List<ClockInInfoStudentStatusVO>> attendanceMap = allAttendanceRates.stream()
//                .collect(Collectors.groupingBy(ClockInInfoStudentStatusVO::getTraineeId));
        List<ClockInInfoStudentStatusVO> allLateInfo = clockInInfoService.getStudentClockingStatusPage(request,buildLateReqVO(classId)).getList();
        Map<Long, List<ClockInInfoStudentStatusVO>> lateMap = allLateInfo.stream()
                .collect(Collectors.groupingBy(ClockInInfoStudentStatusVO::getTraineeId));

        List<TraineeEvaluationDetailPageRespVO> allEvaluationDetails = evaluationResponseService
                .getTraineeEvaluationDetailPage(buildEvaluationReqVO(classId))
                .getList();
        Map<Long, List<TraineeEvaluationDetailPageRespVO>> evaluationMap = allEvaluationDetails.stream()
                .collect(Collectors.groupingBy(TraineeEvaluationDetailPageRespVO::getTraineeId));

//        Map<Integer, Map<Long, List<TraineeLeaveRespVO>>> leaveData = queryLeaveData(classId);
//
//
//        Map<Long, List<AttendanceRateTraineeInfoVO>> attendanceRateMap = queryAttendanceRateData(classId);
//        Map<Long, List<ClockInInfoStudentStatusVO>> lateInfoMap = queryLateInfoData(classId);
//
//
//        Map<Long, List<TraineeEvaluationDetailPageRespVO>> evaluationMap = queryEvaluationRate(classId);

        // 处理每个学员信息
        infoList.forEach(info -> {
            // 根据数据源处理逻辑
            String score = getScoreByDataSource(info, leaveMap, attendanceMap, lateMap, evaluationMap);
            info.setScore(score);
        });

        // 按 traineeId 分组，将 infoList 的数据分类到每个学员
        return groupAndAssembleResults(infoList);
    }

    List<ClassCompletionDO> sortByModuleAndColumn(List<ClassCompletionDO> list) {
        if (list == null || list.size() <= 1) {
            return list;
        }

        // 1. 记录元素的初始索引，确保相对顺序不变
        Map<ClassCompletionDO, Integer> indexMap = new HashMap<>();
        for (int i = 0; i < list.size(); i++) {
            indexMap.put(list.get(i), i);
        }

        // 2. 按 (moduleName, columnName) 分组，保持原始相对顺序
        Map<String, List<ClassCompletionDO>> groupedMap = list.stream()
                .collect(Collectors.groupingBy(
                        item -> item.getModuleName() + "_" + item.getColumnName(),  // 用 moduleName 和 columnName 作为 key
                        LinkedHashMap::new, // 保持插入顺序
                        Collectors.toList()
                ));

        // 3. 按组重新排列，确保稳定性
        List<ClassCompletionDO> sortedList = new ArrayList<>();
        groupedMap.values().forEach(sortedList::addAll);

        return sortedList;
    }


    private Map<Integer, Map<Long, List<TraineeLeaveRespVO>>> queryLeaveData(Integer classId) {
        TraineeLeavePageReqVO leaveReqVO = new TraineeLeavePageReqVO();
        leaveReqVO.setClassId(Long.valueOf(classId));
        leaveReqVO.setPageNo(1);
        leaveReqVO.setPageSize(10000);

        Map<Integer, Map<Long, List<TraineeLeaveRespVO>>> leaveData = new HashMap<>();

        // 批量获取数据
        List<TraineeLeaveRespVO> allLeaveList = leaveService.pageList(buildLeaveReqVO(classId, 10000)).getList();

        leaveReqVO.setLeaveType(1);
        leaveData.put(0, allLeaveList.stream()
                .collect(Collectors.groupingBy(TraineeLeaveRespVO::getTraineeId)));
        leaveReqVO.setLeaveType(2);
        leaveData.put(1, allLeaveList.stream()
                .collect(Collectors.groupingBy(TraineeLeaveRespVO::getTraineeId)));
        leaveReqVO.setLeaveType(3);
        leaveData.put(2, allLeaveList.stream()
                .collect(Collectors.groupingBy(TraineeLeaveRespVO::getTraineeId)));

        return leaveData;
    }

    private Map<Long, List<AttendanceRateTraineeInfoVO>> queryAttendanceRateData(Integer classId) {
        AttendanceRateReqVO attendanceReqVO = new AttendanceRateReqVO();
        attendanceReqVO.setClassId(Long.valueOf(classId));
        return clockInInfoService.getListForAttendanceThreeRate(attendanceReqVO).getTraineeInfoList()
                .stream().collect(Collectors.groupingBy(AttendanceRateTraineeInfoVO::getTraineeId));
    }

    private Map<Long, List<TraineeAttendanceInfoDTO>> queryLateInfoData(Integer classId) {
        AttendanceRateReqVO attendanceReqVO = new AttendanceRateReqVO();
        attendanceReqVO.setClassId(Long.valueOf(classId));
        return clockInInfoService.getLateInfo(attendanceReqVO)
                .stream().collect(Collectors.groupingBy(TraineeAttendanceInfoDTO::getTraineeId));
    }

    private void processStudentInfo(ClassCompletionDO info,
                                    Map<Integer, Map<Long, List<TraineeLeaveRespVO>>> leaveData,
                                    Map<Long, List<AttendanceRateTraineeInfoVO>> attendanceRateMap,
                                    Map<Long, List<ClockInInfoStudentStatusVO>> lateInfoMap,
                                    Map<Long, List<TraineeEvaluationDetailPageRespVO>> evaluationMap) {
        if (info.getScore() != null){
            return;
        }
        if (info.getAcquisitionMode() != null && info.getAcquisitionMode() == 1) {
            if (info.getDataSource() != null) {
                switch (info.getDataSource()) {
                    case 0:
                        Map<Long, List<TraineeLeaveRespVO>> leaveMap = leaveData.get(info.getDataSource());
                        List<TraineeLeaveRespVO> leaves = leaveMap.get(info.getTraineeId());

                        List<TraineeLeaveRespVO> tmpLeave = leaves.stream()
                                .filter(leave -> Objects.equals(leave.getLeaveType(), TraineeLeaveType.BUSINESS.getCode()))
                                .collect(Collectors.toList());
                        info.setScore(String.valueOf(tmpLeave.size()));
                        break;
                    case 1:
                        Map<Long, List<TraineeLeaveRespVO>> leaveMap1 = leaveData.get(info.getDataSource());
                        List<TraineeLeaveRespVO> leaves1 = leaveMap1.get(info.getTraineeId());

                        List<TraineeLeaveRespVO> tmpLeave1 = leaves1.stream()
                                .filter(leave -> Objects.equals(leave.getLeaveType(), TraineeLeaveType.BUSINESS.getCode()))
                                .collect(Collectors.toList());
                        info.setScore(String.valueOf(tmpLeave1.size()));
                        break;
                    case 2:
                        Map<Long, List<TraineeLeaveRespVO>> leaveMap2 = leaveData.get(info.getDataSource());
                        List<TraineeLeaveRespVO> leaves2 = leaveMap2.get(info.getTraineeId());

                        List<TraineeLeaveRespVO> tmpLeave2 = leaves2.stream()
                                .filter(leave -> Objects.equals(leave.getLeaveType(), TraineeLeaveType.BUSINESS.getCode()))
                                .collect(Collectors.toList());
                        info.setScore(String.valueOf(tmpLeave2.size()));
                        break;
                    case 3:
                    case 4:
                    case 5:
                        processAttendanceRates(info, attendanceRateMap);
                        break;
                    case 6:
                        processEvaluationRates(info, evaluationMap);
                        break;
                    case 7:
                        info.setScore(String.valueOf(lateInfoMap.getOrDefault(info.getTraineeId(), Collections.emptyList()).size()));
                        break;
                    default:
                        info.setScore(String.valueOf(0));
                        break;
                }
            }
        } else {
            info.setScore("0");
        }
    }

    private void processEvaluationRates(ClassCompletionDO info, Map<Long, List<TraineeEvaluationDetailPageRespVO>> evaluationMap) {
        List<TraineeEvaluationDetailPageRespVO> rates = evaluationMap.getOrDefault(info.getTraineeId(), Collections.emptyList());

        if (!rates.isEmpty()) {
            TraineeEvaluationDetailPageRespVO traineeInfo = rates.get(0);
//            return String.format("%.2f", traineeInfo.getRatio() * 100);
            info.setScore(String.format("%.2f", traineeInfo.getRatio() * 100));
        }else {
            info.setScore(String.valueOf(0));
        }
    }

    private void processAttendanceRates(ClassCompletionDO info,
                                        Map<Long, List<AttendanceRateTraineeInfoVO>> attendanceRateMap) {
        List<AttendanceRateTraineeInfoVO> rates = attendanceRateMap.getOrDefault(info.getTraineeId(), Collections.emptyList());
        if (!rates.isEmpty()) {
            AttendanceRateTraineeInfoVO traineeInfo = rates.get(0);
            switch (info.getDataSource()) {
                case 3:
                    info.setScore(traineeInfo.getClassRate());
                    break;
                case 4:
                    info.setScore(String.valueOf(traineeInfo.getMealRate()));
                    break;
                case 5:
                    info.setScore(traineeInfo.getAccommodationRate());
                    break;
                default:
                    info.setScore(String.valueOf(0));
                    break;
            }
        } else {
            info.setScore(String.valueOf(0));
        }
    }

    private Map<Long, List<TraineeEvaluationDetailPageRespVO>> queryEvaluationRate(Integer classId) {
        TraineeEvaluationDetailPageReqVO reqVO = new TraineeEvaluationDetailPageReqVO();
        reqVO.setClassId(Long.valueOf(classId));
        List<TraineeEvaluationDetailPageRespVO> list = evaluationResponseService.getTraineeEvaluationDetailPage(reqVO).getList();

        return list.stream().collect(Collectors.groupingBy(TraineeEvaluationDetailPageRespVO::getTraineeId));
    }

    private List<Map<String, Object>> groupAndAssembleResults(List<ClassCompletionDO> infoList) {
        Map<Long, List<ClassCompletionDO>> groupedByTraineeId = infoList.stream()
                .collect(Collectors.groupingBy(ClassCompletionDO::getTraineeId, LinkedHashMap::new, Collectors.toList()));

        List<Map<String, Object>> students = new ArrayList<>();
        groupedByTraineeId.forEach((traineeId, records) -> {
            ClassCompletionDO firstRecord = records.get(0);
            Map<String, Object> student = new HashMap<>();
            student.put("traineeId", firstRecord.getTraineeId().toString());
            student.put("name", firstRecord.getTraineeName());
            student.put("unitPosition", firstRecord.getUnitPosition());
            student.put("classPosition", firstRecord.getClassPosition());
            student.put("groupName", firstRecord.getGroupName());
            student.put("scores", records.stream().map(ClassCompletionDO::getScore).collect(Collectors.toList()));
            students.add(student);
        });

        return students;
    }


    // 构建表头
    // 构建表头逻辑
    private void buildHeader(Sheet sheet, List<String> fixedHeaders, List<Map<String, Map<String, List<String>>>> dynamicHeaders, List<String> lastHeaders) {
        Row row1 = sheet.createRow(0); // 第一行表头
        Row row2 = sheet.createRow(1); // 第二行表头
        Row row3 = sheet.createRow(2); // 第三行表头

        // 固定列
        int colIndex = 0;
        for (String header : fixedHeaders) {
            Cell cell = row1.createCell(colIndex);
            cell.setCellValue(header);
            sheet.addMergedRegion(new CellRangeAddress(0, 2, colIndex, colIndex)); // 固定列合并三行
            colIndex++;
        }

        // 动态列
        for (Map<String, Map<String, List<String>>> mainGroup : dynamicHeaders) {
            for (Map.Entry<String, Map<String, List<String>>> groupEntry : mainGroup.entrySet()) {
                String mainHeader = groupEntry.getKey(); // 主标题
                Map<String, List<String>> subGroup = groupEntry.getValue();

                int groupStart = colIndex; // 当前组的起始列

                for (Map.Entry<String, List<String>> subEntry : subGroup.entrySet()) {
                    String subHeader = subEntry.getKey(); // 次级标题
                    List<String> childrenHeaders = subEntry.getValue();

                    int subGroupStart = colIndex; // 次级标题的起始列

                    for (String childHeader : childrenHeaders) {
                        Cell childCell = row3.createCell(colIndex);
                        childCell.setCellValue(childHeader);
                        colIndex++;
                    }

                    // 合并次级标题单元格
                    if (childrenHeaders.size() > 1) {
                        sheet.addMergedRegion(new CellRangeAddress(1, 1, subGroupStart, colIndex - 1));
                    }
                    Cell subCell = row2.createCell(subGroupStart);
                    subCell.setCellValue(subHeader);
                }

                // 合并主标题单元格
                if (colIndex - groupStart > 1) {
                    sheet.addMergedRegion(new CellRangeAddress(0, 0, groupStart, colIndex - 1));
                }
                Cell mainCell = row1.createCell(groupStart);
                mainCell.setCellValue(mainHeader);
            }
        }

        // 最后列
        for (String header : lastHeaders) {
            Cell cell = row1.createCell(colIndex);
            cell.setCellValue(header);
            sheet.addMergedRegion(new CellRangeAddress(0, 2, colIndex, colIndex)); // 合并三行
            colIndex++;
        }
    }


    // 填充学生数据
    private void fillStudentData(Workbook workbook, Sheet sheet, List<Map<String, Object>> students, Integer type, int size) {
        int rowNum = 3; // 从第四行开始填充数据
        String previousGroupName = null; // 记录上一个小组名

        for (Map<String, Object> student : students) {
            String groupName = (String) student.get("groupName");

            // 检查当前小组名是否需要创建
            if (!Objects.equals("未分组",groupName) && !groupName.equals(previousGroupName)) {
                // 创建一行作为小组名
                Row groupRow = sheet.createRow(rowNum++);
                Cell cell = groupRow.createCell(0);
                cell.setCellValue(groupName);

                // 设置单元格样式
                CellStyle style = workbook.createCellStyle();
                style.setAlignment(HorizontalAlignment.CENTER); // 水平居中
                style.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中
                cell.setCellStyle(style);

                // 合并单元格
                sheet.addMergedRegion(new CellRangeAddress(rowNum - 1, rowNum - 1, 0, size));

                // 更新已处理的小组名
                previousGroupName = groupName;
            }

            // 创建学生数据行
            Row row = sheet.createRow(rowNum++);
            int colNum = 0;

            // 填充固定列
            row.createCell(colNum++).setCellValue((String) student.get("traineeId"));
            row.createCell(colNum++).setCellValue((String) student.get("name"));
            row.createCell(colNum++).setCellValue((String) student.get("unitPosition"));
            row.createCell(colNum++).setCellValue((String) student.get("classPosition"));

            // 填充动态列
            if (type == 1) {
                List<String> scores = (List<String>) student.get("scores");
                for (String score : scores) {
                    row.createCell(colNum++).setCellValue(score);
                }
            }
        }
    }

}

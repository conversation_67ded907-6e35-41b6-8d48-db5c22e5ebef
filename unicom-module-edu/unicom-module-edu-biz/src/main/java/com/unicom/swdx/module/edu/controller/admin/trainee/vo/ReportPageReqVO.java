package com.unicom.swdx.module.edu.controller.admin.trainee.vo;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Set;

/**
 * @ClassName: RegistrationPageReqVO
 * @Author: lty
 * @Date: 2024/10/9 14:32
 */
@Data
@ApiModel(value = "报名详情分页请求VO")
public class ReportPageReqVO extends PageParam {

    @ApiModelProperty(value = "班级id")
    private Long classId;

    @ApiModelProperty(value = "报到状态 1：未报到 2已报到")
    private Integer reportStatus;

    @ApiModelProperty(value = "班级名称")
    private String className;

    @ApiModelProperty(value = "学员姓名")
    private String name;

    @ApiModelProperty(value = "校区")
    private String campus;

    @ApiModelProperty(value = "学员手机号")
    private String phone;

    @ApiModelProperty(value = "报到日期")
    private String reportDateBeg;
    private String reportDateEnd;
    private Set<Integer> includeColumnIndexes;
    @ApiModelProperty(value = "报到状态 1：未报到 2已报到")
    private Integer status;
    private Integer orderStatus;


    @ApiModelProperty(value = "班级属性")
    private Long classAttribute;

    @ApiModelProperty(value = "教师id")
    private Long teacherId;
}

package com.unicom.swdx.module.edu.controller.admin.clockininfo.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 考勤签到 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class ClockInInfoBaseVO {

    @ApiModelProperty(value = "班级id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long classId;

    @ApiModelProperty(value = "学员id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long traineeId;

    @ApiModelProperty(value = "排课表id")
    private Long classCourseId;

    @ApiModelProperty(value = "0-到课，1-就餐，2-住宿")
    private Integer type;

    @ApiModelProperty(value = "0-早餐，1-午餐，2-晚餐（就餐专属）")
    private Integer mealPeriod;

    @ApiModelProperty(value = "0-未打卡，1-已打卡，2-迟到，3-请假")
    private Integer traineeStatus;

    @ApiModelProperty(value = "学员打卡时间")
    private LocalDateTime clockingTime;

    @ApiModelProperty(value = "请假类型1事假2病假3五会假")
    private Integer leaveType;

    @ApiModelProperty(value = "学员姓名")
    private String name;
}

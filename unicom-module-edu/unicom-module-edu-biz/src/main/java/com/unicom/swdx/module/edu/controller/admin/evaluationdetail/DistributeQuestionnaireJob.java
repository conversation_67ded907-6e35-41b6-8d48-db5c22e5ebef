package com.unicom.swdx.module.edu.controller.admin.evaluationdetail;

import com.unicom.swdx.framework.tenant.core.aop.TenantIgnore;
import com.unicom.swdx.module.edu.controller.admin.evaluationdetail.vo.EvaluationDetailSaveReqVO;
import com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo.EvaluationResponseSaveReqVO;
import com.unicom.swdx.module.edu.controller.admin.questionmanagement.vo.QuestionManagementRespVO;
import com.unicom.swdx.module.edu.controller.admin.questionnairemanagement.vo.QuestionnaireManagementRespVO;
import com.unicom.swdx.module.edu.dal.dataobject.classcourse.ClassCourseDO;
import com.unicom.swdx.module.edu.dal.dataobject.classmanagement.ClassManagementDO;
import com.unicom.swdx.module.edu.dal.dataobject.training.TraineeDO;
import com.unicom.swdx.module.edu.dal.mysql.classcourse.ClassCourseMapper;
import com.unicom.swdx.module.edu.dal.mysql.classmanagement.ClassManagementMapper;
import com.unicom.swdx.module.edu.service.classcommen.CommenService;
import com.unicom.swdx.module.edu.service.evaluationdetail.EvaluationDetailService;
import com.unicom.swdx.module.edu.service.evaluationresponse.EvaluationResponseService;
import com.unicom.swdx.module.edu.service.questionnairemanagement.QuestionnaireManagementService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.boot.autoconfigure.klock.annotation.Klock;
import org.springframework.boot.autoconfigure.klock.model.LockTimeoutStrategy;
import org.springframework.boot.autoconfigure.klock.model.LockType;
import org.springframework.boot.autoconfigure.klock.model.ReleaseTimeoutStrategy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class DistributeQuestionnaireJob {

    @Resource
    ClassCourseMapper classCourseMapper;

    @Resource
    ClassManagementMapper classManagementMapper;

    @Resource
    QuestionnaireManagementService questionnaireManagementService;

    @Resource
    CommenService commenService;

    @Resource
    EvaluationResponseService evaluationResponseService;

    @Resource
    EvaluationDetailService evaluationDetailService;

//    @XxlJob("DistributeQuestionnaireJob")
//    @TenantIgnore
//    @Klock(
//            lockType = LockType.Reentrant, // 使用可重入锁
//            waitTime = 10, // 获取锁的等待时间
//            leaseTime = 180, // 锁的持续时间，单位是秒
//            lockTimeoutStrategy = LockTimeoutStrategy.FAIL_FAST, // 获取锁失败时的策略
//            releaseTimeoutStrategy = ReleaseTimeoutStrategy.FAIL_FAST // 释放锁失败时的策略
//    )
    //@Scheduled(cron = "0 */1 8-18 * * MON-FRI")
//    public void distributeQuestionnaire() throws Exception {
//
//        // 查询近半个小时内没有下发问卷且已经结束的课程
//        LocalDateTime now = LocalDateTime.now();
//
//        XxlJobHelper.log("开始下发问卷");
//        LocalDateTime time = now.minus(1, ChronoUnit.DAYS);
//        List<ClassCourseDO> endCourses = classCourseMapper.getEndedCourse(time, now);
//        endCourses.addAll(classCourseMapper.getDepartmentCourse(time, now));
//        if(endCourses != null && !endCourses.isEmpty()) {
//            // 下发问卷
//            endCourses.forEach(endCourse -> {
//                try {
//                    endCourse.setIsDistributed(true);
//                    Long classId = endCourse.getClassId();
//                    ClassManagementDO classManagementDO = classManagementMapper.selectById(classId);
//                    // 获取对应问卷信息
//                    Long questionnaireId = questionnaireManagementService.getByEducateForm(endCourse.getEducateFormId(), endCourse.getTenantId());
//                    if (questionnaireId == null) {
//                        questionnaireId = questionnaireManagementService.getDefaultQuestionnaireId(endCourse.getTenantId());
//                    }
//                    QuestionnaireManagementRespVO questionnaire = questionnaireManagementService.getQuestionnaireManagement(questionnaireId);
//                    List<QuestionManagementRespVO> questions = questionnaire.getQuestions();
//
//                    // 获取课程对应学员信息
//                    List<TraineeDO> trainee = new ArrayList<>();
//                    if (classManagementDO.getEvaluate().equals(2)) { //不是考勤评课的情况
//                        trainee = commenService.getUsersByClasscourseid(endCourse.getId(), endCourse.getClassId());
//                    } else {
//                        trainee = commenService.getCheckedUserByClassCourseId(endCourse.getId(), endCourse.getClassId());
//                    }
//                    List<Long> traineeIdList =  evaluationResponseService.getExistEvaluationTrainee(endCourse.getId());
//                    trainee = trainee.stream().filter(t -> !traineeIdList.contains(t.getId())).collect(Collectors.toList());
//                    if (!trainee.isEmpty()) {
//                        trainee.forEach(traineeDO -> {
//                            EvaluationResponseSaveReqVO responseSaveReqVO = new EvaluationResponseSaveReqVO();
//                            responseSaveReqVO.setClassCourseId(endCourse.getId());
//                            responseSaveReqVO.setQuestionnaireId(questionnaire.getId());
//                            responseSaveReqVO.setStudentId(traineeDO.getId());
//                            responseSaveReqVO.setIssuer(endCourse.getTeacherIdString());
//                            responseSaveReqVO.setTeacherId(endCourse.getTeacherIdString());
//                            responseSaveReqVO.setHandle(false);
//                            responseSaveReqVO.setTenantId(endCourse.getTenantId());
//                            responseSaveReqVO.setDepartment(endCourse.getDepartment());
//                            if (Boolean.TRUE.equals(questionnaire.getTimeTag())) {
//                                LocalDateTime expireTime = now.plusDays(questionnaire.getTimeLimit());
//                                responseSaveReqVO.setExpireTime(expireTime);
//                            }
//
//                            // 下发问卷
//                            evaluationResponseService.createEvaluationResponse(responseSaveReqVO);
//
//                            try {
//                                questions.forEach(question -> {
//                                    EvaluationDetailSaveReqVO detailSaveReqVO = new EvaluationDetailSaveReqVO();
//                                    detailSaveReqVO.setQuestionnaireId(questionnaire.getId());
//                                    detailSaveReqVO.setQuestionId(question.getId());
//                                    detailSaveReqVO.setQuestionType(question.getQuestionType());
//                                    detailSaveReqVO.setStudentId(traineeDO.getId());
//                                    detailSaveReqVO.setClassCourseId(endCourse.getId());
//                                    detailSaveReqVO.setTenantId(endCourse.getTenantId());
//
//                                    // 记录每个学员的选项
//                                    evaluationDetailService.createEvaluationDetail(detailSaveReqVO);
//                                });
//                            } catch (Exception e) {
//                                e.printStackTrace();
//                            }
//
//                        });
//                    }
//                    classCourseMapper.updateById(endCourse);
//                } catch (Exception e) {
//                    e.printStackTrace();
//                }
//            });
//            // 下发完成后更新下发状态为已下发
//            // classCourseMapper.updateBatch(endCourses);
//            XxlJobHelper.log("问卷下发完成");
//        }
//    }
}

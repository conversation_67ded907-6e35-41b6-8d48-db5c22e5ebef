package com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@AllArgsConstructor
@NoArgsConstructor
// 设置 chain = false，避免导入有问题
@Accessors(chain = false)
public class TeacherInformationImportResultExcelVO extends TeacherInformationImportExcelVO {

    @ExcelProperty(value = "失败原因", index = 17)
    @HeadStyle(fillForegroundColor = 10, horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
    private String error;

}

package com.unicom.swdx.module.edu.controller.admin.todoitems.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 班主任待办事项 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class TodoItemsBaseVO implements Serializable {

    @ApiModelProperty(value = "待办事项的具体内容", example = "{}")
    private String content;

    @ApiModelProperty(value = "待办状态，0-未办 1-已办", required = true, example = "1")
    @NotNull(message = "待办状态，0-未办 1-已办不能为空")
    private Integer status;

    @ApiModelProperty(value = "待办事项类型编码(0请假申请、1报名确认)", required = true, example = "1")
    @NotNull(message = "待办事项类型编码(0请假申请、1报名确认)不能为空")
    private Integer type;

    @ApiModelProperty(value = "班级ID", required = true, example = "1")
    @NotNull(message = "班级ID不能为空")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long classId;

    @ApiModelProperty(value = "代办事项相关人员ID", example = "1")
    private Long personId;

    @ApiModelProperty(value = "问卷id", example = "1")
    private Long questionnaireId;
}

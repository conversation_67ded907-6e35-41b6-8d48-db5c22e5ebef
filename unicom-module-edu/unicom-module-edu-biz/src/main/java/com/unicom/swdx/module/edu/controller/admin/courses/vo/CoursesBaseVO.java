package com.unicom.swdx.module.edu.controller.admin.courses.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

/**
 * 课程库 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class CoursesBaseVO {

    @ApiModelProperty(value = "课程类型(1-专题课、2-选修课、3-教学活动)", required = true)
    @Range(min = 1, max = 3, message = "课程类型不存在")
    private Integer coursesType;

    @ApiModelProperty(value = "课程名称", required = true)
    private String name;

    @ApiModelProperty(value = "别名")
    @Length(max = 50, message = "别名长度不能超过 50 个字符")
    private String shortName;

    @ApiModelProperty(value = "课程分类字典ID")
    private Long themeId;

    @ApiModelProperty(value = "教学形式字典ID")
    private Long educateFormId;

    @ApiModelProperty(value = "管理部门字典ID(人事系统部门)")
    private Long managementDeptId;

    @ApiModelProperty(value = "状态，0启用，1封库")
    @Range(min = 0, max = 1, message = "状态不存在")
    private Integer status;

    @ApiModelProperty(value = "开发时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate date;

    @ApiModelProperty(value = "教学活动库活动类型字典ID")
    private Long activityType;

    @ApiModelProperty(value = "关联授课教师ID列表")
    private List<Long> teacherIds;

    @ApiModelProperty(value = "教学点ID")
    private Long teachingPointId;

//    @ApiModelProperty(value = "是否党政领导讲课，0不是，1是")
//    private Boolean isLeaderLecture;
}
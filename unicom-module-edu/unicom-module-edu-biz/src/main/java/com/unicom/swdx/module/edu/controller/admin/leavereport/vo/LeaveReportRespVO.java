package com.unicom.swdx.module.edu.controller.admin.leavereport.vo;

import lombok.Data;

import java.time.LocalDateTime;
@Data
public class LeaveReportRespVO {
    private Long id;
    /**
     * 离校报备名称
     */
    private String name;
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    /**
     * 班级id
     */
    private Long classId;
    /**
     * 填报状态
     */
    private Integer status;
    /**
     * 教师id
     */
    private Long teacherId;

    private Long unfilled;

    private Long leaveCount;

    private Long stayCount;
}

package com.unicom.swdx.module.edu.controller.admin.signupunit.vo;

import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import io.swagger.annotations.*;

@ApiModel("管理后台 - EduSignUpUnit Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SignUpUnitRespVO extends SignUpUnitBaseVO {

    @ApiModelProperty(value = "主键id，自增", required = true)
    private Integer id;

    @ApiModelProperty(value = "创建时间", required = true)
    private LocalDateTime createTime;

    private Integer index;

    private Long tenantId;
}

package com.unicom.swdx.module.edu.controller.admin.cadreInformation;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.util.validation.ExcelValidator;
import com.unicom.swdx.framework.excel.core.util.ExcelUtils;
import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import com.unicom.swdx.module.edu.controller.admin.cadreInformation.excelimporthandler.GetCadreUnitWriteHandler;
import com.unicom.swdx.module.edu.controller.admin.cadreInformation.vo.*;
import com.unicom.swdx.module.edu.controller.admin.trainee.excelimporthandler.*;
import com.unicom.swdx.module.edu.controller.admin.trainee.vo.AddTraineeInfoReqVO;
import com.unicom.swdx.module.edu.controller.admin.trainee.vo.EditTraineeInfoReqVO;
import com.unicom.swdx.module.edu.controller.admin.trainee.vo.TraineeInfoImportStrExcelVO;
import com.unicom.swdx.module.edu.convert.cadreInformation.CadreInformationConvert;
import com.unicom.swdx.module.edu.dal.mysql.signupunit.SignUpUnitMapper;
import com.unicom.swdx.module.edu.dal.mysql.training.TraineeMapper;
import com.unicom.swdx.module.edu.service.cadreInformation.CadreInformationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.Normalizer;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.unicom.swdx.framework.common.enums.ErrorCodeConstants.EXPORT_FAILED;
import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.IMPORT;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.CLASS_MANAGEMENT_HEAD_NAME_ERROR;

@Api(tags = "管理后台 - 学员干部信息")
@RestController
@RequestMapping("/edu/cadre")
@Validated
public class CadreInformationController {

    @Resource
    private CadreInformationService cadreInformationService;

    @Resource
    private SignUpUnitMapper signUpUnitMapper;

    @Resource
    private TraineeMapper traineeMapper;
    /**
     * 干部信息分页查询
     * @param reqVO 入参
     * @return 干部信息分页结果
     */
    @GetMapping("/page")
    @ApiOperation(value = "干部信息分页查询")
    public CommonResult<Page<PageCadreInformationRespVO>> pageCadreInfo(PageCadreInformationReqVO reqVO) {
        return success(cadreInformationService.pageCadreInformation(reqVO));
    }

    /**
     * 新增干部信息
     * @param reqVO 入参
     * @return 干部信息ID
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增干部信息")
    public CommonResult<Long> addCadreInfo(@Valid @RequestBody AddTraineeInfoReqVO reqVO) {
        return success(cadreInformationService.addCadreInformation(reqVO));
    }


    /**
     * 编辑干部信息
     * @param reqVO 入参
     * @return 干部信息ID
     */
    @PostMapping("/edit")
    @ApiOperation(value = "编辑干部信息")
    public CommonResult<Long> editCadreInfo(@Valid @RequestBody EditTraineeInfoReqVO reqVO) {
        return success(cadreInformationService.editCadreInformation(reqVO));
    }


    /**
     * 批量删除干部信息
     * @param id 干部id
     * @return true 删除成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "单个删除干部信息")
    public CommonResult<Boolean> batchDelete(Long id) {
        return success(cadreInformationService.delete(id));
    }


    /**
     * 批量删除干部信息
     * @param reqVO 入参
     * @return true 删除成功
     */
    @PostMapping("/batchDelete")
    @ApiOperation(value = "批量删除干部信息")
    public CommonResult<Boolean> batchDelete(@RequestBody CadreBatchDeleteReqVO reqVO) {
        return success(cadreInformationService.batchDelete(reqVO));
    }



    @GetMapping("/get-import-templates")
    @ApiOperation("获得导入干部信息模板")
    public void importTemplate(Long unitId, HttpServletResponse response) throws IOException {
        // 输出
        String filename = "干部信息模板.xls";
        String sheetName = "干部信息模板";
        response.setHeader("Access-Control-Expose-Headers","Content-Disposition");
        response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(filename, "UTF-8"));
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        // 输出 Excel
        try {

            List<TraineeInfoImportExcelVO> list = getTraineeInfoImportStrExcelVOS();
            EasyExcel.write(response.getOutputStream(), TraineeInfoImportExcelVO.class)
                    .autoCloseStream(false) // 不要自动关闭，交给 Servlet 自己处理
                    .registerWriteHandler(new GetEducationSheetWriteHandler(traineeMapper,null))
                    .registerWriteHandler(new GetLevelSheetWriteHandler(traineeMapper))
                    .registerWriteHandler(new GetCadreUnitWriteHandler(unitId,signUpUnitMapper))
                    .registerWriteHandler(new GetNationSheetWriteHandler(traineeMapper))
                    .registerWriteHandler(new GetzzmmSheetWriteHandler(traineeMapper))
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()) // 基于 column 长度，自动适配。最大 255 宽度
                    .sheet(sheetName).doWrite(list);
        } catch (IOException e) {
            response.setContentType("application/json;charset=UTF-8");
            throw exception(EXPORT_FAILED);
        }
    }


    @PostMapping("/import")
    @ApiOperation("导入干部信息")
    @OperateLog(type = IMPORT)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "Excel 文件", required = true, dataTypeClass = MultipartFile.class),
            @ApiImplicitParam(name = "unitId", value = "单位id", required = true, dataTypeClass = Long.class)
    })
    public CommonResult<CadreImportRespVO> importExcel(@RequestParam("file") MultipartFile file,
                                                       @RequestParam Long unitId,
                                                       @RequestParam Integer cover) {

        // 获取文件名并去除路径和扩展名
        String originalFileName = file.getOriginalFilename();

        if (originalFileName == null || originalFileName.isEmpty()) {
            throw new IllegalArgumentException("文件名为空");
        }

        // 获取文件名（不带路径和扩展名）
        String fileNameWithoutExtension = new File(originalFileName).getName().substring(0, originalFileName.lastIndexOf('.')).trim();

        // 标准化处理
        Normalizer.normalize(fileNameWithoutExtension, Normalizer.Form.NFC);

        List<CadreInfoImportExcelVO> list;
        // 校验表头信息
        try{
            list = ExcelUtils.readFirstSheetAndCheckHead(file, CadreInfoImportExcelVO.class);
        }catch (Exception e){
            throw exception(CLASS_MANAGEMENT_HEAD_NAME_ERROR);
        }

        // 验证 Excel 数据
        ExcelValidator.valid(list,1);

        // 调用服务层，导入学生信息
        return success(cadreInformationService.importInfo(list,unitId,cover));
    }



    /**
     * 获取单个干部信息
     * @param id 干部信息主键
     * @return 获取单个干部信息
     */
    @GetMapping("/get")
    @ApiOperation(value = "获取单个干部信息")
    public CommonResult<CadreInformationRespVO> getById(Long id) {
        return success(CadreInformationConvert.INSTANCE.covertInfo(cadreInformationService.getById(id)));
    }



    /**
     * 导出干部信息
     * @param reqVO 入参
     */
    @PostMapping("/export")
    @ApiOperation(value = "导出干部信息")
    public void exportCadreInfo(@RequestBody ExportCadreInformationReqVO reqVO, HttpServletResponse response) throws IOException {
        List<ExportCadreInfoExcelVO> list = cadreInformationService.getCadreInfoList(reqVO);
        ExcelUtils.writeByIncludeColumnIndexes(response, "干部信息.xls",
                "数据", ExportCadreInfoExcelVO.class,list, reqVO.getIncludeColumnIndexes());
    }



    /**
     * 干部信息详情分页
     * @param reqVO 干部信息入参
     * @return 干部信息详情分页
     */
    @GetMapping("/getDetailPage")
    @ApiOperation(value = "获取干部信息详情")
    public CommonResult<Page<CadreInfoDetailRespVO>> getDetail(@Valid CadreInfoDetailReqVO reqVO)  {
        Page<CadreInfoDetailRespVO> page = cadreInformationService.getCadreInfoDetailPage(reqVO);
        return success(page);
    }

    /**
     * 导出干部信息详情
     * @param reqVO 干部信息入参
     */
    @PostMapping("/exportCadreInfoDetail")
    @ApiOperation(value = "导出干部信息详情")
    public void exportCadreInfoDetail(@Valid @RequestBody CadreInfoDetailReqVO reqVO, HttpServletResponse response) throws IOException {
        List<ExportCadreInfoDetailExcelVO> list = cadreInformationService.getCadreInfoDetailList(reqVO);
        ExcelUtils.writeByIncludeColumnIndexes(response, "干部信息详情.xls",
                "数据", ExportCadreInfoDetailExcelVO.class,list, reqVO.getIncludeColumnIndexes());
    }


    //-----------------------------------------------------------参训报名-----------------------------------------------------------------//

    /**
     * 参训报名分页查询
     * @param reqVO 入参
     * @return 参训报名分页结果
     */
    @GetMapping("/traineeReportPage")
    @ApiOperation(value = "参训报名分页查询")
    public CommonResult<PageResult<TraineeReportPageRespVO>> traineeReportPage(TraineeReportPageReqVO reqVO) {
        return success(cadreInformationService.traineeReportPage(reqVO));
    }

    /**
     * 导出参训报名
     * @param reqVO 参训报名入参
     */
    @PostMapping("/exportTraineeReport")
    @ApiOperation(value = "导出参训报名")
    public void exportTraineeReport(@Valid @RequestBody TraineeReportPageReqVO reqVO, HttpServletResponse response) throws IOException {
        List<TraineeReportExcelVO> list = cadreInformationService.exportTraineeReport(reqVO);

        ExcelUtils.writeByIncludeColumnIndexes(response, "参训报名.xls",
                "数据", TraineeReportExcelVO.class,list, reqVO.getIncludeColumnIndexes());
    }



    /**
     * 参训报名 未报名人员分页
     * @param reqVO 入参
     * @return 学员信息
     */
    @GetMapping("/traineeInfo")
    @ApiOperation(value = "参训报名 未报名人员分页")
    public CommonResult<Page<TraineeInfoPageRespVO>> traineeInfoByClassIdAndUnitId(TraineeInfoPageReqVO reqVO) {
        return success(cadreInformationService.traineeInfoByUnitId(reqVO));
    }



    /**
     * 确认报名
     * @param reqVO 入参
     * @return 学员信息
     */
    @PostMapping("/confirm")
    @ApiOperation(value = "确认报名")
    public CommonResult<CadreImportRespVO> confirm(@RequestBody TraineeConfirmReqVO reqVO) {
        return success(cadreInformationService.confirm(reqVO));
    }

    private List<TraineeInfoImportExcelVO> getTraineeInfoImportStrExcelVOS() {
        List<TraineeInfoImportExcelVO> list = new ArrayList<>();
        TraineeInfoImportExcelVO vo = new TraineeInfoImportExcelVO();
        vo.setIndex("示例：1（示例请勿删除）");        // 序号（示例中的特殊格式）
        vo.setName("张三");                            // 学员姓名
        vo.setPhone("18612345678");                   // 学员手机号（截取前11位，假设示例数据有误）
        vo.setCardNo("******************");           // 学员身份证
        vo.setUnitName("测试单位");                     // 学员所在单位
        vo.setEducationalLevelName("硕士研究生");        // 文化程度
        vo.setEthnicName("汉族");                      // 学员民族
        vo.setPosition("科员");                        // 学员职务
        vo.setJobLevelName("一级科员");                 // 职级
        vo.setPoliticalIdentityName("中共党员");        // 政治面貌
        vo.setGraduationSchool("湖南大学");             // 毕业院校
        vo.setRemark("备注说明");                       // 学员备注
        list.add(vo);
        return list;
    }
}

package com.unicom.swdx.module.edu.controller.admin.cadreInformation.vo;

import com.unicom.swdx.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@ApiModel("管理后台 - EduClassManagement分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TraineeReportPageReqVO extends PageParam {

    @ApiModelProperty(value = "班次名称")
    private String className;

    @ApiModelProperty(value = "年度")
    private Integer year;

    @ApiModelProperty(value = "班级状态，0-报名中，1-报名结束，2-开班中，3-已结束，4-未开始")
    private Integer classStatus;

    @ApiModelProperty(value = "单位id")
    private Long unitId;

    @ApiModelProperty(value = "排序")
    private Integer order;

    @ApiModelProperty(value = "排序类型 asc升序 desc降序")
    private String orderType;

    @ApiModelProperty(value = "导出特定列")
    private List<Integer> includeColumnIndexes;

}

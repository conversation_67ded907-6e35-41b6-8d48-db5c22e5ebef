package com.unicom.swdx.module.edu.controller.admin.coursechange.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.unicom.swdx.module.edu.controller.admin.courses.vo.CoursesBaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@ApiModel("管理后台 - 调课记录 Response VO")
@Data
@ToString(callSuper = true)
public class CourseChangeRespVO {

    @ApiModelProperty(value = "调课记录id")
    private Long id;

    @ApiModelProperty(value = "班次id")
    private Long classId;

    @ApiModelProperty(value = "班次名称")
    private String className;

    @ApiModelProperty(value = "调课时间")
    private LocalDateTime changeTime;

    @ApiModelProperty(value = "调课类型")
    private Integer changeType;

    @ApiModelProperty(value = "调课人id")
    private Long changeUserId;

    @ApiModelProperty(value = "调课人姓名")
    private String changeUserName;

    @ApiModelProperty(value = "调课理由")
    private String changeReason;

    @ApiModelProperty(value = "调课前课程信息")
    private String classInfoBefore;

    @ApiModelProperty(value = "调课前课程信息拆分")
    private Map<String, Object> classInfoBeforeDetail;

    @ApiModelProperty(value = "调课后课程信息")
    private String classInfoAfter;

    @ApiModelProperty(value = "调课后课程信息拆分")
    private Map<String, Object> classInfoAfterDetail;

    @ApiModelProperty(value = "班次属性")
    private Integer classAttribute;

}

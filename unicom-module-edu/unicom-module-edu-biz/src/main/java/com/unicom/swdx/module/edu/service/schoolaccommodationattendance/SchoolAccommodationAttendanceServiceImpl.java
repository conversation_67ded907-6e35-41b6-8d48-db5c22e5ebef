package com.unicom.swdx.module.edu.service.schoolaccommodationattendance;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unicom.swdx.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.unicom.swdx.module.edu.dal.dataobject.classclockcalendar.ClassClockCalendarDO;
import com.unicom.swdx.module.edu.dal.mysql.classclockcalendar.ClassClockCalendarMapper;
import com.unicom.swdx.module.edu.service.classcourse.ClassCourseService;
import com.unicom.swdx.module.edu.service.clockininfo.ClockInInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CompletableFuture;

import com.unicom.swdx.module.edu.controller.admin.schoolaccommodationattendance.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.schoolaccommodationattendance.SchoolAccommodationAttendanceDO;
import com.unicom.swdx.framework.common.pojo.PageResult;

import com.unicom.swdx.module.edu.convert.schoolaccommodationattendance.SchoolAccommodationAttendanceConvert;
import com.unicom.swdx.module.edu.dal.mysql.schoolaccommodationattendance.SchoolAccommodationAttendanceMapper;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.*;

/**
 * 全校就餐住宿考勤 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SchoolAccommodationAttendanceServiceImpl extends ServiceImpl<SchoolAccommodationAttendanceMapper, SchoolAccommodationAttendanceDO> implements SchoolAccommodationAttendanceService {

    @Resource
    private SchoolAccommodationAttendanceMapper schoolAccommodationAttendanceMapper;

    @Resource
    private ClassCourseService classCourseService;

    @Resource
    private ClassClockCalendarMapper classClockCalendarMapper;

    @Resource
    private ClockInInfoService clockInInfoService;


    @Override
    public Integer createSchoolAccommodationAttendance(SchoolAccommodationAttendanceCreateReqVO createReqVO) {

        // 获取一年中的天数
        List<SchoolAccommodationAttendanceBaseVO> yearData = generateYearData(createReqVO.getYear());

        // 获取节假日
        Set<String> result = classCourseService.getVacation(createReqVO.getYear(), 0);

        List<SchoolAccommodationAttendanceDO> batchInsertList = new ArrayList<>();

        for (int i = 0; i < yearData.size(); i++) {
            SchoolAccommodationAttendanceBaseVO vo = yearData.get(i);

            // 将 LocalDate 转换为字符串
            String formattedDate = vo.getClockDate().toString();

            boolean isHoliday = result.contains(formattedDate);

            // 插入日期
            createReqVO.setClockDate(vo.getClockDate());

            if (isHoliday) {
                // 1- 关
                createReqVO.setBreakfast(1);
                createReqVO.setLunch(1);
                createReqVO.setDinner(1);
                createReqVO.setPutUp(1);
                // 是节假日
                createReqVO.setIsHoliday(0);
            } else {
                // 0 - 开
                createReqVO.setBreakfast(0);
                createReqVO.setLunch(0);
                createReqVO.setDinner(0);
                createReqVO.setPutUp(0);
                // 非节假日
                createReqVO.setIsHoliday(1);
            }

            // 检查当天为工作日，第二天为休息日的情况
            if (!isHoliday && i + 1 < yearData.size()) {
                String nextDayFormattedDate = yearData.get(i + 1).getClockDate().toString();
                if (result.contains(nextDayFormattedDate)) {
                    // 第二天下班不考勤
                    createReqVO.setDinner(1);
                    createReqVO.setPutUp(1);
                }
            }

            // 检查当天为休息日，第二天为工作日的情况
            if (isHoliday && i + 1 < yearData.size()) {
                String nextDayFormattedDate = yearData.get(i + 1).getClockDate().toString();
                if (!result.contains(nextDayFormattedDate)) {
                    // 第二天上班不考勤
                    createReqVO.setPutUp(0);
                }
            }

            // 转换并添加到批量插入列表
            SchoolAccommodationAttendanceDO schoolAccommodationAttendance = SchoolAccommodationAttendanceConvert.INSTANCE.convert(createReqVO);
            batchInsertList.add(schoolAccommodationAttendance);
        }

        // 批量插入
        if (!batchInsertList.isEmpty()) {
            schoolAccommodationAttendanceMapper.insertBatch(batchInsertList);
        }

        // 返回
        return yearData.size();
    }

    @Override
    public void updateSchoolAccommodationAttendance(SchoolAccommodationAttendanceUpdateReqVO updateReqVO) {
        // 校验存在
        this.validateSchoolAccommodationAttendanceExists(updateReqVO.getId());
        // 更新
        SchoolAccommodationAttendanceDO updateObj = SchoolAccommodationAttendanceConvert.INSTANCE.convert(updateReqVO);
        schoolAccommodationAttendanceMapper.updateById(updateObj);

        LambdaQueryWrapperX<ClassClockCalendarDO> lqw=new LambdaQueryWrapperX<>();
        lqw.eqIfPresent(ClassClockCalendarDO::getClockDate,updateObj.getClockDate());
        List<ClassClockCalendarDO> classClockCalendarDOS = classClockCalendarMapper.selectList(lqw);
        for (ClassClockCalendarDO classClockCalendarDO : classClockCalendarDOS) {
            classClockCalendarDO.setBreakfast(updateObj.getBreakfast());
            classClockCalendarDO.setLunch(updateObj.getLunch());
            classClockCalendarDO.setDinner(updateObj.getDinner());
            classClockCalendarDO.setPutUp(updateObj.getPutUp());
        }

        if (!classClockCalendarDOS.isEmpty()){
            classClockCalendarMapper.updateBatch(classClockCalendarDOS);
        }

        //修改考勤日历后刷新签到表
        // 刷新签到表,立即提交任务，不等待结果
        CompletableFuture.runAsync(() -> clockInInfoService.generateRecords())
                .exceptionally(e -> {
                    log.error(e.getMessage(), e);
                    return null;
                });
    }

    @Override
    public void updateSchoolAccommodationAttendanceBatch(SchoolAccommodationAttendanceParamsVO updateReqVO) {
        List<Integer> ids = updateReqVO.getIds();
        //todo 优化
        for(Integer id : ids){
            // 校验存在
            this.validateSchoolAccommodationAttendanceExists(id);
            // 更新
            updateReqVO.getSchoolAccommodationAttendanceUpdateReqVO().setId(id);
            SchoolAccommodationAttendanceDO updateObj = SchoolAccommodationAttendanceConvert.INSTANCE.convert(updateReqVO.getSchoolAccommodationAttendanceUpdateReqVO());
            schoolAccommodationAttendanceMapper.updateById(updateObj);

            updateObj=schoolAccommodationAttendanceMapper.selectById(updateObj.getId());
            LambdaQueryWrapperX<ClassClockCalendarDO> lqw=new LambdaQueryWrapperX<>();
            lqw.eqIfPresent(ClassClockCalendarDO::getClockDate,updateObj.getClockDate());
            List<ClassClockCalendarDO> classClockCalendarDOS = classClockCalendarMapper.selectList(lqw);
            for (ClassClockCalendarDO classClockCalendarDO : classClockCalendarDOS) {
                classClockCalendarDO.setBreakfast(updateObj.getBreakfast());
                classClockCalendarDO.setLunch(updateObj.getLunch());
                classClockCalendarDO.setDinner(updateObj.getDinner());
                classClockCalendarDO.setPutUp(updateObj.getPutUp());
            }
            if (!classClockCalendarDOS.isEmpty()){
                classClockCalendarMapper.updateBatch(classClockCalendarDOS);
            }
        }
        //修改考勤日历后刷新签到表
        // 刷新签到表,立即提交任务，不等待结果
        CompletableFuture.runAsync(() -> clockInInfoService.generateRecords())
                .exceptionally(e -> {
                    log.error(e.getMessage(), e);
                    return null;
                });
    }

    @Override
    public void deleteSchoolAccommodationAttendance(Integer id) {
        // 校验存在
        this.validateSchoolAccommodationAttendanceExists(id);
        // 删除
        schoolAccommodationAttendanceMapper.deleteById(id);
    }

    private void validateSchoolAccommodationAttendanceExists(Integer id) {
        if (schoolAccommodationAttendanceMapper.selectById(id) == null) {
            throw exception(SCHOOL_ACCOMMODATION_ATTENDANCE_NOT_EXISTS);
        }
    }

    @Override
    public SchoolAccommodationAttendanceDO getSchoolAccommodationAttendance(Integer id) {
        return schoolAccommodationAttendanceMapper.selectById(id);
    }

    @Override
    public List<SchoolAccommodationAttendanceDO> getSchoolAccommodationAttendanceList(Collection<Integer> ids) {
        return schoolAccommodationAttendanceMapper.selectBatchIds(ids);
    }

    @Override
    public List<SchoolAccommodationAttendanceDO> getSchoolAccommodationAttendancePage(SchoolAccommodationAttendancePageReqVO pageReqVO) {
        return schoolAccommodationAttendanceMapper.selectDateRangeList(pageReqVO);
    }


    /**
     * 创建一年的所有日期
     * @param year
     * @return
     */
    public static List<SchoolAccommodationAttendanceBaseVO> generateYearData(int year) {
        List<SchoolAccommodationAttendanceBaseVO> yearData = new ArrayList<>();
        LocalDate startDate = LocalDate.of(year, 1, 1);
        LocalDate endDate = LocalDate.of(year, 12, 31);

        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            SchoolAccommodationAttendanceBaseVO vo = new SchoolAccommodationAttendanceBaseVO();
            vo.setClockDate(currentDate);
            yearData.add(vo);
            currentDate = currentDate.plusDays(1);
        }

        return yearData;
    }

}

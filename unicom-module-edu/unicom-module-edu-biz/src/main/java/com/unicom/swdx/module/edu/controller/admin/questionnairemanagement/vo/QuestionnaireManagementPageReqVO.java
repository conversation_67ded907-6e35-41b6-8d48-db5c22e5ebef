package com.unicom.swdx.module.edu.controller.admin.questionnairemanagement.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.unicom.swdx.framework.common.pojo.PageParam;
import org.hibernate.validator.constraints.Range;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 评估问卷管理分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class QuestionnaireManagementPageReqVO extends PageParam {

    @Schema(description = "问卷标题")
    private String title;

    @Schema(description = "问卷副标题")
    private String subtitle;

    @Schema(description = "是否默认问卷 (0: 否, 1: 是)")
    private String isDefault;

    @Schema(description = "状态 0未发布1已发布 2 已结束", example = "1")
    private String status;

    @Schema(description = "创建部门")
    private Long createDept;

    @Schema(description = "导出选中的id")
    private String selectedIdList;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "创建人")
    private Long creator;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "专题库教学形式")
    private String topicEducateForm;

    @Schema(description = "最低分")
    private Integer lowscore;

    @Schema(description = "启用最低分")
    private Integer lowscoreTag;

    @Schema(description = "最低字数")
    private Integer lowword;

    @Schema(description = "启用最低字数")
    private Integer lowwordTag;

    @Schema(description = "启用时效限制")
    private Integer timeTag;

    @Schema(description = "失效天数")
    private Integer timeLimit;

    @ApiModelProperty(value = "排序字段(默认按创建时间) 0-按id 1-按问卷状态")
    @Range(min = 0, max = 1, message = "无法按该字段进行排序")
    private Integer sortField;

    @ApiModelProperty(value = "是否降序(默认降序)")
    private Boolean isDesc;

    @ApiModelProperty(value = "序号是否倒排(默认正排)")
    private Boolean isSerialDesc;

    @Schema(description = "是否内置", example = "false")
    private Boolean builtIn;

    @ApiModelProperty(value = "指定导出列索引(为空则全部导出)")
    private Set<Integer> includeColumnIndexes;

    @Schema(description = "查询开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @Schema(description = "查询结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
}
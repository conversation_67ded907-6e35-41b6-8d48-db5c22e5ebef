package com.unicom.swdx.module.edu.service.electivetraineeselection;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.util.date.DateUtils;
import com.unicom.swdx.framework.excel.core.util.ExcelUtils;
import com.unicom.swdx.framework.mybatis.core.util.MyBatisUtils;
import com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils;
import com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.electiverelease.AppElectiveReleaseTraineeRespVO;
import com.unicom.swdx.module.edu.controller.admin.electiverelease.vo.electiverelease.ElectiveReleasePageRespVO;
import com.unicom.swdx.module.edu.controller.admin.electivetraineeselection.dto.ElectiveTraineeSelectedCoursesAndReleaseDTO;
import com.unicom.swdx.module.edu.controller.admin.electivetraineeselection.vo.*;
import com.unicom.swdx.module.edu.convert.electivetraineeselection.ElectiveTraineeSelectionConvert;
import com.unicom.swdx.module.edu.dal.dataobject.classcourse.ClassCourseDO;
import com.unicom.swdx.module.edu.dal.dataobject.courses.CoursesDO;
import com.unicom.swdx.module.edu.dal.dataobject.electiverelease.ElectiveReleaseDO;
import com.unicom.swdx.module.edu.dal.dataobject.electivetraineeselection.ElectiveTraineeSelectionDO;
import com.unicom.swdx.module.edu.dal.dataobject.training.TraineeDO;
import com.unicom.swdx.module.edu.dal.mysql.classcourse.ClassCourseMapper;
import com.unicom.swdx.module.edu.dal.mysql.electiverelease.ElectiveReleaseMapper;
import com.unicom.swdx.module.edu.dal.mysql.electivereleasecourses.ElectiveReleaseCoursesMapper;
import com.unicom.swdx.module.edu.dal.mysql.electivetraineeselection.ElectiveTraineeSelectionMapper;
import com.unicom.swdx.module.edu.dal.mysql.training.TraineeMapper;
import com.unicom.swdx.module.edu.enums.courses.CoursesTypeEnum;
import com.unicom.swdx.module.edu.enums.electivetraineeselection.ElectiveTraineeSelectionStatusEnum;
import com.unicom.swdx.module.edu.enums.trainee.TraineeDictTypeEnum;
import com.unicom.swdx.module.edu.enums.trainee.TraineeStatusEnum;
import com.unicom.swdx.module.edu.service.classmanagement.ClassManagementService;
import com.unicom.swdx.module.edu.service.electiverelease.ElectiveReleaseService;
import com.unicom.swdx.module.edu.service.training.TraineeDictConvertService;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.*;
import static com.unicom.swdx.module.edu.utils.serialnumber.PageDataSerialNumberUtil.generateSerialNumberList;

/**
 * 选修课学员选课 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ElectiveTraineeSelectionServiceImpl implements ElectiveTraineeSelectionService {

    @Resource
    private ElectiveTraineeSelectionMapper electiveTraineeSelectionMapper;

    @Resource
    private TraineeMapper traineeMapper;

    @Resource
    private TraineeDictConvertService traineeDictConvertService;

    @Resource
    private ElectiveReleaseService electiveReleaseService;

    @Resource
    private ElectiveReleaseMapper electiveReleaseMapper;

    @Resource
    private ElectiveReleaseCoursesMapper electiveReleaseCoursesMapper;

    @Resource
    private ClassManagementService classManagementService;

    @Resource
    private ClassCourseMapper classCourseMapper;

    @Override
    public Long createElectiveTraineeSelection(ElectiveTraineeSelectionCreateReqVO createReqVO) {
        // 通过登录账号获取学员
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        // 获取学员信息
        TraineeDO traineeDO = traineeMapper.selectByUserId(userId);
        if (Objects.isNull(traineeDO)) {
            throw exception(ELECTIVE_TRAINEE_USER_NOT_EXISTS);
        }
        // 获取前端学员选择的选修课发布信息
        ElectiveReleaseDO electiveReleaseDO = electiveReleaseMapper.selectById(createReqVO.getReleaseId());
        if (Objects.isNull(electiveReleaseDO)) {
            throw exception(ELECTIVE_RELEASE_NOT_EXISTS);
        }

        // 1.学员当前已经选择的选修课
        List<ElectiveTraineeSelectedCoursesAndReleaseDTO> selectedElectiveCoursesList = electiveTraineeSelectionMapper
                .getSelectedCoursesListByTraineeId(traineeDO.getId());

        checkSelectedElectiveCoursesConflict(selectedElectiveCoursesList, electiveReleaseDO);

        // 2.该学员班级上的必修课(专题、教学活动)
        List<ClassCourseDO> classCourseDOList = classCourseMapper.getCoursesListByClassIdAndCourseTypelist(traineeDO.getClassId(),
                Arrays.asList(CoursesTypeEnum.TOPIC_COURSE.getType(), CoursesTypeEnum.TEACHING_ACTIVITY.getType()));

        // 上课时间冲突判断
        checkSelectedClassCoursesConflict(classCourseDOList, electiveReleaseDO);
        // 插入选课
        ElectiveTraineeSelectionDO electiveTraineeSelectionDO = new ElectiveTraineeSelectionDO();
        electiveTraineeSelectionDO.setReleaseId(createReqVO.getReleaseId());
        electiveTraineeSelectionDO.setReleaseCourseId(createReqVO.getReleaseCourseId());
        electiveTraineeSelectionDO.setTraineeId(traineeDO.getId());
        electiveTraineeSelectionMapper.insert(electiveTraineeSelectionDO);
        return electiveTraineeSelectionDO.getId();
    }


    /**
     * 批量创建选修课学员选课
     *
     * @param batchCreateReqVO 学员批量选课
     * @return 是否成功
     */
    @Override
    public Boolean batchCreateElectiveTraineeSelection(List<ElectiveTraineeSelectionCreateReqVO> batchCreateReqVO) {
        // 批量选课不能为空
        if (Objects.isNull(batchCreateReqVO) || batchCreateReqVO.isEmpty()) {
            throw exception(ELECTIVE_TRAINEE_SELECTED_COURSE_LIST_IS_EMPTY);
        }
        // 通过登录账号获取学员
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        // 获取学员信息
        TraineeDO traineeDO = traineeMapper.selectByUserId(userId);
        if (Objects.isNull(traineeDO)) {
            throw exception(ELECTIVE_TRAINEE_USER_NOT_EXISTS);
        }
        // 获取前端学员选择的所有选修课发布信息
        List<Long> releaseIdList = batchCreateReqVO.stream()
                .map(ElectiveTraineeSelectionCreateReqVO::getReleaseId)
                .collect(Collectors.toList());
        if (releaseIdList.isEmpty()) {
            throw exception(ELECTIVE_TRAINEE_SELECTED_COURSE_LIST_IS_EMPTY);
        }
        List<ElectiveReleaseDO> electiveReleaseDOList = electiveReleaseMapper.selectBatchIds(releaseIdList);
        if (electiveReleaseDOList.size() < releaseIdList.size()) {
            throw exception(ELECTIVE_RELEASE_NOT_EXISTS);
        }
        // 批量选课之间互相校验是否上课时间冲突
        checkSelectedElectiveCoursesListSelfConflict(electiveReleaseDOList);

        // 1.学员当前已经选择的选修课
        List<ElectiveTraineeSelectedCoursesAndReleaseDTO> selectedElectiveCoursesList = electiveTraineeSelectionMapper
                .getSelectedCoursesListByTraineeId(traineeDO.getId());

        // 批量选课与当前学员已选选修课校验
        for (ElectiveReleaseDO electiveReleaseDO : electiveReleaseDOList) {
            checkSelectedElectiveCoursesConflict(selectedElectiveCoursesList, electiveReleaseDO);
        }
        // 2.该学员班级上的必修课(专题、教学活动)
        List<ClassCourseDO> classCourseDOList = classCourseMapper.getCoursesListByClassIdAndCourseTypelist(traineeDO.getClassId(),
                Arrays.asList(CoursesTypeEnum.TOPIC_COURSE.getType(), CoursesTypeEnum.TEACHING_ACTIVITY.getType()));
        // 批量选课与当前学员班级专题、教学活动课程校验
        for (ElectiveReleaseDO electiveReleaseDO : electiveReleaseDOList) {
            checkSelectedClassCoursesConflict(classCourseDOList, electiveReleaseDO);
        }

        List<ElectiveTraineeSelectionDO> batchCreateDOList = batchCreateReqVO.stream().map(createReqVO -> {
            ElectiveTraineeSelectionDO electiveTraineeSelectionDO = new ElectiveTraineeSelectionDO();
            electiveTraineeSelectionDO.setReleaseId(createReqVO.getReleaseId());
            electiveTraineeSelectionDO.setReleaseCourseId(createReqVO.getReleaseCourseId());
            electiveTraineeSelectionDO.setTraineeId(traineeDO.getId());
            return electiveTraineeSelectionDO;
        }).collect(Collectors.toList());
        if (!batchCreateDOList.isEmpty()) {
            electiveTraineeSelectionMapper.insertBatch(batchCreateDOList);
        }
        return true;
    }

    /**
     * 批量选课之间互相校验上课时间冲突
     *
     * @param electiveReleaseDOList 选课列表
     */
    private void checkSelectedElectiveCoursesListSelfConflict(List<ElectiveReleaseDO> electiveReleaseDOList) {
        // 如果列表为空或只有一个元素，则不可能存在时间冲突
        if (Objects.isNull(electiveReleaseDOList) || electiveReleaseDOList.size() <= 1) {
            return;
        }

        // 对选课列表按上课开始时间进行排序
        electiveReleaseDOList.sort(Comparator.comparing(ElectiveReleaseDO::getClassStartTime));

        // 遍历排序后的列表，检查相邻课程是否有时间重叠
        for (int i = 1; i < electiveReleaseDOList.size(); i++) {
            ElectiveReleaseDO currentCourse = electiveReleaseDOList.get(i);
            ElectiveReleaseDO previousCourse = electiveReleaseDOList.get(i - 1);

            // 如果当前课程的开始时间早于或等于前一课程的结束时间，则存在时间冲突
            if (!currentCourse.getClassStartTime().isAfter(previousCourse.getClassEndTime())) {
                throw exception(ELECTIVE_TRAINEE_SELECTED_COURSE_TIME_SELF_CONFLICT);
            }
        }
    }

    /**
     * 检测已选择的班级课程时间冲突
     *
     * @param classCourseDOList 班级课程
     * @param electiveReleaseDO 当前选课
     */
    private static void checkSelectedClassCoursesConflict(List<ClassCourseDO> classCourseDOList, ElectiveReleaseDO electiveReleaseDO) {
        classCourseDOList.forEach(classCourseDO -> {
            if (DateUtils.isTimeRangesOverlap(electiveReleaseDO.getClassStartTime(),
                    electiveReleaseDO.getClassEndTime(),
                    classCourseDO.getBeginTime(),
                    classCourseDO.getEndTime())) {
                throw exception(ELECTIVE_TRAINEE_SELECTED_CLASS_COURSE_TIME_CONFLICT);
            }
        });
    }

    /**
     * 检测已选择的选修课时间冲突
     *
     * @param selectedElectiveCoursesList 已选择的选修课
     * @param electiveReleaseDO           当前选课
     */
    private static void checkSelectedElectiveCoursesConflict(List<ElectiveTraineeSelectedCoursesAndReleaseDTO> selectedElectiveCoursesList, ElectiveReleaseDO electiveReleaseDO) {
        // 上课时间冲突判断
        for (ElectiveTraineeSelectedCoursesAndReleaseDTO dto : selectedElectiveCoursesList) {
            if (DateUtils.isTimeRangesOverlap(electiveReleaseDO.getClassStartTime(),
                    electiveReleaseDO.getClassEndTime(),
                    dto.getClassStartTime(),
                    dto.getClassEndTime())) {
                throw exception(ELECTIVE_TRAINEE_SELECTED_ELECTIVE_COURSE_TIME_CONFLICT);
            }
        }
    }

    /**
     * 发布课程已选课人员信息分页
     *
     * @param reqVO 请求参数
     * @return 分页结果
     */
    @Override
    public PageResult<ElectiveCourseTraineeSelectedRespVO> getCourseSelectedTraineePage(ElectiveCourseTraineeSelectedPageReqVO reqVO) {
        // 设置班主任班级范围
        if (Boolean.TRUE.equals(reqVO.getIsClassMaster())) {
            reqVO.setClassIdList(classManagementService.getLoginUserClassMasterLimitClassList());
        }
        IPage<ElectiveCourseTraineeSelectedRespVO> page = MyBatisUtils.buildPage(reqVO);
        List<ElectiveCourseTraineeSelectedRespVO> pageResultList = electiveTraineeSelectionMapper.getCourseSelectedTraineePage(page, reqVO);
        Map<Long, Map<String, String>> educationalLevel = traineeDictConvertService.getDictDateMapById(TraineeDictTypeEnum.EDUCATIONAL_LEVEL.getType());
        Map<Long, Map<String, String>> politicalIdentityMap = traineeDictConvertService.getDictDateMapById(TraineeDictTypeEnum.POLITICAL_IDENTITY.getType());
        pageResultList.forEach(item -> {
            if (Objects.nonNull(educationalLevel)) {
                Map<String, String> educationalLevelMap = educationalLevel.getOrDefault(item.getEducationalLevel(), null);
                if (Objects.nonNull(educationalLevelMap)) {
                    item.setEducationalLevelStr(educationalLevelMap.getOrDefault("label", null));
                }
            }
            if (Objects.nonNull(politicalIdentityMap)) {
                Map<String, String> politicalIdentityMap1 = politicalIdentityMap.getOrDefault(item.getPoliticalIdentity(), null);
                if (Objects.nonNull(politicalIdentityMap1)) {
                    item.setPoliticalIdentityStr(politicalIdentityMap1.getOrDefault("label", null));
                }
            }
        });
        // 序号
        // 添加序号字段
        List<Long> serialNumberList = generateSerialNumberList(reqVO.getIsSerialDesc(),
                page.getTotal(),
                reqVO,
                pageResultList.size());
        for (int i = 0; i < pageResultList.size(); i++) {
            pageResultList.get(i).setSerialNumber(serialNumberList.get(i));
        }
        return new PageResult<>(pageResultList, page.getTotal());
    }

    /**
     * 导出发布课程已选课人员信息
     *
     * @param reqVO    请求参数
     * @param response 响应参数
     */
    @Override
    public void exportCourseSelectedTraineeExcel(ElectiveCourseTraineeSelectedPageReqVO reqVO, HttpServletResponse response) throws IOException {
        // 根据发布课程id 查询发布课程信息
        CoursesDO coursesDO = electiveReleaseCoursesMapper.selectCourseByReleaseCourseId(reqVO.getReleaseCourseId());
        if (Objects.isNull(coursesDO)) {
            throw exception(ELECTIVE_RELEASE_COURSES_NOT_EXISTS);
        }
        reqVO.setPageNo(1);
        reqVO.setPageSize(Integer.MAX_VALUE);
        List<ElectiveCourseTraineeSelectedRespVO> list = getCourseSelectedTraineePage(reqVO).getList();
        List<ElectiveCourseTraineeSelectedExcelVO> excelList = ElectiveTraineeSelectionConvert.INSTANCE.convertList(list);
        // 导出 Excel
        ExcelUtils.writeByIncludeColumnIndexes(response, coursesDO.getName() + "学员名单.xls",
                "数据", ElectiveCourseTraineeSelectedExcelVO.class, excelList, reqVO.getIncludeColumnIndexes());
    }

    /**
     * 获取选修课发布信息选课情况信息列表
     *
     * @param reqVO 请求参数
     * @return 选修课发布信息选课情况信息列表
     */
    @Override
    public List<AppElectiveTraineeSelectionSimpleRespVO> getCourseSelectionTraineeList(AppElectiveTraineeSelectionReleaseReqVO reqVO) {
        // 获取已选课学员信息
        List<AppElectiveTraineeSelectionSimpleRespVO> list = electiveTraineeSelectionMapper.selectSelectionTraineeInfoList(reqVO);
        // 已选
        if (ElectiveTraineeSelectionStatusEnum.SELECTED.getStatus().equals(reqVO.getStatus())) {
            return list;
        }
        // 学员id -> 学员信息 字典
        Map<Long, AppElectiveTraineeSelectionSimpleRespVO> idToInfo = list.stream().collect(Collectors.toMap(AppElectiveTraineeSelectionSimpleRespVO::getTraineeId,
                item -> item, (old, now) -> old));
        List<AppElectiveTraineeSelectionSimpleRespVO> respVOList;
        List<Integer> statusList = Arrays.asList(TraineeStatusEnum.REPORTED.getStatus(),
                TraineeStatusEnum.GRADUATED.getStatus());
        // 获取班级全部已报到、已结业学员信息
        List<TraineeDO> traineeDOList = traineeMapper.selectListByClassIdAndStatus(reqVO.getClassId(),
                statusList);
        if (ElectiveTraineeSelectionStatusEnum.UNSELECT.getStatus().equals(reqVO.getStatus())) {
            // 未选课
            respVOList = traineeDOList.stream().filter(item -> !idToInfo.containsKey(item.getId()))
                    .map(this::convertToAppElectiveTraineeSelectionSimpleRespVO)
                    .collect(Collectors.toList());
        } else {
            // 全部和默认
            respVOList = traineeDOList.stream().map(item -> convertToAppElectiveTraineeSelectionSimpleRespVOByIdToInfo(item, idToInfo))
                    .collect(Collectors.toList());
        }
        return respVOList;
    }

    /**
     * 班主任移动端-选修课管理-班级是否存在未选学员(气泡)
     *
     * @param classId 班级ID
     * @return 是否存在未选学员
     */
    @Override
    public boolean existUnSelectTeacher(Long classId) {
        List<ElectiveReleasePageRespVO> electiveReleaseListByClassId = electiveReleaseService.getElectiveReleaseListByClassId(classId);
        for (ElectiveReleasePageRespVO electiveReleasePageRespVO : electiveReleaseListByClassId) {
            if (Objects.nonNull(electiveReleasePageRespVO)
                    && electiveReleasePageRespVO.getSelectedNum() < electiveReleasePageRespVO.getSelectionNum()) {
                return true;
            }
        }
        return false;
    }

    /**
     * 学员移动端-选修课-是否存在未选选修课(气泡)
     *
     * @return 是否存在未选选修课
     */
    @Override
    public boolean existUnSelectTrainee() {
        // 获取学员未选的选修课
        List<AppElectiveReleaseTraineeRespVO> res = electiveReleaseService.getElectiveReleaseAndCoursesListByTraineeIdAndStatus(
                ElectiveTraineeSelectionStatusEnum.UNSELECT.getStatus());
        return Objects.nonNull(res) && !res.isEmpty();
    }

    @Override
    public void deleteElectiveInfoByTraineeIds(List<Long> traineeIds) {
        List<ElectiveTraineeSelectionDO> list = electiveTraineeSelectionMapper.getElectiveInfoByTraineeIds(traineeIds);
        if (CollUtil.isEmpty(list)){
            return;
        }
        electiveTraineeSelectionMapper.deleteBatchIds(list.stream().map(ElectiveTraineeSelectionDO::getId).collect(Collectors.toList()));
    }

    private AppElectiveTraineeSelectionSimpleRespVO convertToAppElectiveTraineeSelectionSimpleRespVOByIdToInfo(TraineeDO traineeDO, Map<Long, AppElectiveTraineeSelectionSimpleRespVO> idToInfo) {
        if (idToInfo.containsKey(traineeDO.getId())) {
            return idToInfo.get(traineeDO.getId());
        }
        return convertToAppElectiveTraineeSelectionSimpleRespVO(traineeDO);
    }

    private AppElectiveTraineeSelectionSimpleRespVO convertToAppElectiveTraineeSelectionSimpleRespVO(TraineeDO traineeDO) {
        if (Objects.isNull(traineeDO)) {
            return null;
        }
        AppElectiveTraineeSelectionSimpleRespVO info = new AppElectiveTraineeSelectionSimpleRespVO();
        info.setTraineeId(traineeDO.getId());
        info.setName(traineeDO.getName());
        info.setSex(traineeDO.getSex());
        info.setPhone(traineeDO.getPhone());
        return info;
    }
}

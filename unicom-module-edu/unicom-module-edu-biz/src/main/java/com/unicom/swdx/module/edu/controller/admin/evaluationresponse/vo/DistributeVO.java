package com.unicom.swdx.module.edu.controller.admin.evaluationresponse.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 课程评价记录撤回 Request VO")

@Data
public class DistributeVO {
    private Long id;

    @Schema(description = "评估结果主键",  example = "15291")
    private Long questionnaireId;

    @Schema(description = "评估结果主键",  example = "15291")
    private Long classId;

    @Schema(description = "评估结果主键",  example = "15291")
    private Long studentId;
}

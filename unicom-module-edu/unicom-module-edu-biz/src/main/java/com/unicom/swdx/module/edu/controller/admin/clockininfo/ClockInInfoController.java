package com.unicom.swdx.module.edu.controller.admin.clockininfo;

import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.excel.core.util.ExcelUtils;
import com.unicom.swdx.framework.security.core.util.SecurityFrameworkUtils;
import com.unicom.swdx.framework.tenant.core.aop.NonRepeatSubmit;
import com.unicom.swdx.framework.tenant.core.aop.TenantIgnore;
import com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.*;
import com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.attendancerate.*;
import com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.businesscenter.attendancerate.AttendanceRateForBusinessCenterReqVO;
import com.unicom.swdx.module.edu.controller.admin.clockininfo.vo.businesscenter.attendancerate.AttendanceRateForBusinessCenterRespVO;
import com.unicom.swdx.module.edu.convert.clockininfo.ClockInInfoConvert;
import com.unicom.swdx.module.edu.dal.dataobject.clockininfo.ClockInInfoDO;
import com.unicom.swdx.module.edu.service.clockininfo.ClockInInfoService;
import com.unicom.swdx.module.edu.service.training.TraineeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Collection;
import java.util.List;

import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

@Api(tags = "管理后台 - 考勤签到")
@RestController
@RequestMapping("/edu/clock-in-info")
@Validated
public class ClockInInfoController {

    @Resource
    private ClockInInfoService clockInInfoService;

    @Resource
    private TraineeService traineeService;

    @PostMapping("/create")
    @ApiOperation("创建考勤签到")
    @PreAuthorize("@ss.hasPermission('edu:clock-in-info:create')")
    public CommonResult<Integer> createClockInInfo(@Valid @RequestBody ClockInInfoCreateReqVO createReqVO) {
        return success(clockInInfoService.createClockInInfo(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation("更新考勤签到")
    @PreAuthorize("@ss.hasPermission('edu:clock-in-info:update')")
    public CommonResult<Boolean> updateClockInInfo(@Valid @RequestBody ClockInInfoUpdateReqVO updateReqVO) {
        clockInInfoService.updateClockInInfo(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @ApiOperation("删除考勤签到")
    @ApiImplicitParam(name = "id", value = "编号", required = true, dataTypeClass = Integer.class)
    @PreAuthorize("@ss.hasPermission('edu:clock-in-info:delete')")
    public CommonResult<Boolean> deleteClockInInfo(@RequestParam("id") Integer id) {
        clockInInfoService.deleteClockInInfo(id);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation("获得考勤签到")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Integer.class)
    @PreAuthorize("@ss.hasPermission('edu:clock-in-info:query')")
    public CommonResult<ClockInInfoRespVO> getClockInInfo(@RequestParam("id") Integer id) {
        ClockInInfoDO clockInInfo = clockInInfoService.getClockInInfo(id);
        return success(ClockInInfoConvert.INSTANCE.convert(clockInInfo));
    }

    @GetMapping("/list")
    @ApiOperation("获得考勤签到列表")
    @ApiImplicitParam(name = "ids", value = "编号列表", required = true, example = "1024,2048", dataTypeClass = List.class)
    @PreAuthorize("@ss.hasPermission('edu:clock-in-info:query')")
    public CommonResult<List<ClockInInfoRespVO>> getClockInInfoList(@RequestParam("ids") Collection<Integer> ids) {
        List<ClockInInfoDO> list = clockInInfoService.getClockInInfoList(ids);
        return success(ClockInInfoConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/view-details-page")
    @ApiOperation("查看考勤数据详情")
    @PreAuthorize("@ss.hasPermission('edu:clock-in-info:query')")
    public CommonResult<PageResult<ClockInInfoReturnVO>> getClockInInfoPage(HttpServletRequest request, @Valid ClockInInfoPageReqVO pageVO) {
        PageResult<ClockInInfoReturnVO> pageResult = clockInInfoService.getClockInInfoPage(request,pageVO);
        return success(pageResult);
    }

    @PostMapping("/listForAttendanceThreeRate")
    @ApiOperation("考勤三率")
    @PreAuthorize("@ss.hasPermission('edu:clock-in-info:query')")
    public CommonResult<AttendanceRateRespVO> getListForAttendanceThreeRate(HttpServletRequest request,@Valid @RequestBody AttendanceRateReqVO reqVO) {
        // 通过登录账号检查学员权限并禁止其访问
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        traineeService.checkTrainPermissionAndAccessDenied(request,userId);
        AttendanceRateRespVO respVO = clockInInfoService.getListForAttendanceThreeRate(reqVO);
        return success(respVO);
    }

    @PostMapping("/listForAttendanceThreeRateForBusinessCenter")
    @ApiOperation("考勤三率-业中首页-仪表盘")
    @PreAuthorize("@ss.hasPermission('edu:clock-in-info:query')")
    @TenantIgnore
    @PermitAll
    public CommonResult<AttendanceRateForBusinessCenterRespVO> getListForAttendanceThreeRate(@Valid @RequestBody AttendanceRateForBusinessCenterReqVO reqVO) {
        AttendanceRateForBusinessCenterRespVO respVO = clockInInfoService.getListForAttendanceThreeRateForBusinessCenter(reqVO);
        return success(respVO);
    }

    @PostMapping("/getClassTraineeNotArrivedAndLeaveInfo")
    @ApiOperation("获取学生上课未到、请假详情列表")
    @PreAuthorize("@ss.hasPermission('edu:clock-in-info:query')")
    public CommonResult<List<AttendanceTraineeClassInfoRespVO>> getClassTraineeNotArrivedAndLeaveInfo(HttpServletRequest request,@Valid @RequestBody AttendanceNotArrivedAndLeaveInfoReqVO reqVO) {
        // 通过登录账号检查学员权限并禁止其访问
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        traineeService.checkTrainPermissionAndAccessDenied(request,userId);
        List<AttendanceTraineeClassInfoRespVO> respVO = clockInInfoService.getClassTraineeNotArrivedAndLeaveInfo(reqVO);
        return success(respVO);
    }

    @PostMapping("/detailsForAppTeacher")
    @ApiOperation("班主任移动端-班级考勤-获取班级某天考勤详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "date", value = "考勤日期", example = "2024-10-10", required = true, dataTypeClass = String.class),
            @ApiImplicitParam(name = "classId", value = "班级id", example = "1", required = true, dataTypeClass = Long.class)
    })
    @PreAuthorize("@ss.hasPermission('edu:clock-in-info:query')")
    public CommonResult<List<AppAttendanceDetailsRespVO>> getDetailsForAppTeacher(HttpServletRequest request,
                                                                                  @RequestParam("date") String date,
                                                                                  @RequestParam("classId") Long classId) {
        // 通过登录账号检查学员权限并禁止其访问
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        traineeService.checkTrainPermissionAndAccessDenied(request,userId);
        List<AppAttendanceDetailsRespVO> respVO = clockInInfoService.getDetailsForAppTeacher(date, classId);
        return success(respVO);
    }

    @PostMapping("/getAbnormalAttendanceDateList")
    @ApiOperation("班主任移动端-班级考勤-获取班级一月考勤异常的日期列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "date", value = "考勤年月", example = "2024-10", required = true, dataTypeClass = String.class),
            @ApiImplicitParam(name = "classId", value = "班级id", example = "1", required = true, dataTypeClass = Long.class)
    })
    @PreAuthorize("@ss.hasPermission('edu:clock-in-info:query')")
    public CommonResult<List<String>> getAbnormalAttendanceDateList(HttpServletRequest request,
                                                                    @RequestParam("date") String date,
                                                                    @RequestParam("classId") Long classId) {
        // 通过登录账号检查学员权限并禁止其访问
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        traineeService.checkTrainPermissionAndAccessDenied(request,userId);
        List<String> respVO = clockInInfoService.getAbnormalAttendanceDateList(date, classId);
        return success(respVO);
    }

    @PostMapping("/getTraineeAttendanceDetail")
    @ApiOperation("班主任移动端-班级考勤-获取考勤项学员考勤具体情况列表")
    @PreAuthorize("@ss.hasPermission('edu:clock-in-info:query')")
    public CommonResult<List<AppTraineeAttendanceDetailsRespVO>> getTraineeAttendanceDetail(HttpServletRequest request,@Valid @RequestBody AppTraineeAttendanceDetailsReqVO reqVO) {
        // 通过登录账号检查学员权限并禁止其访问
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        traineeService.checkTrainPermissionAndAccessDenied(request,userId);
        List<AppTraineeAttendanceDetailsRespVO> respVO = clockInInfoService.getTraineeAttendanceDetails(reqVO);
        return success(respVO);
    }

    @PostMapping("/updateTraineeAttendanceStatus")
    @ApiOperation("班主任移动端-班级考勤-更新学员打卡状态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "recordId", value = "学员打卡记录id", example = "1", required = true, dataTypeClass = String.class),
            @ApiImplicitParam(name = "status", value = "学员打卡状态 0-未到 1-正常 2-迟到 3-事假 4-病假 5-五会假", example = "0", required = true, dataTypeClass = Long.class)
    })
    @PreAuthorize("@ss.hasPermission('edu:clock-in-info:query')")
    public CommonResult<Boolean> updateTraineeAttendanceStatus(HttpServletRequest request,
                                                               @RequestParam("recordId") Long recordId,
                                                               @RequestParam("status") Integer status) {
        // 通过登录账号检查学员权限并禁止其访问
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        traineeService.checkTrainPermissionAndAccessDenied(request,userId);
        clockInInfoService.updateTraineeAttendanceStatus(recordId, status);
        return success(true);
    }

    @PostMapping("/batchUpdateToCheckIn")
    @ApiOperation("班主任移动端-班级考勤-批量学员补卡")
    @PreAuthorize("@ss.hasPermission('edu:clock-in-info:query')")
    public CommonResult<Boolean> batchUpdateToCheckIn(HttpServletRequest request,@Valid @RequestBody AppBatchUpdateToCheckInReqVO reqVO) {
        // 通过登录账号检查学员权限并禁止其访问
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        traineeService.checkTrainPermissionAndAccessDenied(request,userId);
        clockInInfoService.batchUpdateToCheckIn(reqVO);
        return success(true);
    }

    @PostMapping("/getMealTraineeNotArrivedAndLeaveInfo")
    @ApiOperation("获取学生就餐未到、请假详情列表")
    @PreAuthorize("@ss.hasPermission('edu:clock-in-info:query')")
    public CommonResult<List<AttendanceTraineeMealInfoRespVO>> getMealTraineeNotArrivedAndLeaveInfo(HttpServletRequest request,@Valid @RequestBody AttendanceNotArrivedAndLeaveInfoReqVO reqVO) {
        // 通过登录账号检查学员权限并禁止其访问
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        traineeService.checkTrainPermissionAndAccessDenied(request,userId);
        List<AttendanceTraineeMealInfoRespVO> respVO = clockInInfoService.getMealTraineeNotArrivedAndLeaveInfo(reqVO);
        return success(respVO);
    }

    @PostMapping("/getAccommodationTraineeNotArrivedAndLeaveInfo")
    @ApiOperation("获取学生住宿未到、请假详情列表")
    @PreAuthorize("@ss.hasPermission('edu:clock-in-info:query')")
    public CommonResult<List<AttendanceTraineeAccommodationInfoRespVO>> getAccommodationTraineeNotArrivedAndLeaveInfo(HttpServletRequest request,@Valid @RequestBody AttendanceNotArrivedAndLeaveInfoReqVO reqVO) {
        // 通过登录账号检查学员权限并禁止其访问
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        traineeService.checkTrainPermissionAndAccessDenied(request,userId);
        List<AttendanceTraineeAccommodationInfoRespVO> respVO = clockInInfoService.getAccommodationTraineeNotArrivedAndLeaveInfo(reqVO);
        return success(respVO);
    }

    @GetMapping("/myClockInInfo")
    @ApiOperation("获得我的考勤")
    @PreAuthorize("@ss.hasPermission('edu:clock-in-info:query')")
    public CommonResult<MyClockInInfoRespVO> getMyClockInInfo(MyClockInInfoReqVO reqVO) {
        return success(clockInInfoService.getMyClockInInfoList(reqVO));
    }

    @GetMapping("/traineeClockInfo")
    @ApiOperation("移动端-学员获取本人的日常考勤信息")
    @PreAuthorize("@ss.hasPermission('edu:clock-in-info:query')")
    @ApiImplicitParam(name = "type", value = "考勤类型:0=到课, 1=就餐, 2=住宿", required = true, example = "0", dataTypeClass = Integer.class)
    public CommonResult<AppClockInInfoRespVO> getAppClockInfo(@RequestParam("type") Integer type) {
        return success(clockInInfoService.getAppClockInfo(type));
    }

    @GetMapping("/traineeRollCallInfo")
    @ApiOperation("移动端-学员获取本人的大课考勤、点名签到、发起学员报道信息")
    @PreAuthorize("@ss.hasPermission('edu:clock-in-info:query')")
    public CommonResult<AppClockInInfoRespVO> getRollCallInfo(@RequestParam("type") Integer type) {
        return success(clockInInfoService.getRollCallInfo(type));
    }

    @PostMapping("/checkIn")
    @ApiOperation("移动端-日常考勤签到")
    @PreAuthorize("@ss.hasPermission('edu:clock-in-info:query')")
    @NonRepeatSubmit(limitType = NonRepeatSubmit.Type.PARAM,lockTime = 5000)  //5s防止重复提交打卡相同内容
    public CommonResult<Boolean> checkInById(@RequestParam("id") Integer id ,@RequestParam(value = "isLate",required = false) Boolean isLate) {
        if(isLate == null){
            isLate = false;
        }
        return success(clockInInfoService.checkInById(id,isLate));
    }

    @PostMapping("/checkInByMobile")
    @ApiOperation("市县接入-日常考勤签到，根据考勤表id签到")
    @PreAuthorize("@ss.hasPermission('edu:clock-in-info:query')")
    @ApiImplicitParam(name = "id", value = "当前考勤信息id", required = true, example = "0", dataTypeClass = Integer.class)
    public CommonResult<Boolean> checkInByIdAndMobile(@RequestParam("id") Integer id) {
        return success(clockInInfoService.checkInByIdAndMobile(id));
    }

    @PostMapping("/checkInLecture")
    @ApiOperation("移动端-大课考勤签到")
    @PreAuthorize("@ss.hasPermission('edu:clock-in-info:query')")
    public CommonResult<Boolean> checkInLectureById(@RequestParam("id") Integer id ,@RequestParam(value = "isLate",required = false) Boolean isLate) {
        if(isLate == null){
            isLate = false;
        }
        return success(clockInInfoService.checkInLectureById(id,isLate));
    }

    @PostMapping("/checkInRollCall")
    @ApiOperation("移动端-点名签到")
    @PreAuthorize("@ss.hasPermission('edu:clock-in-info:query')")
    public CommonResult<Boolean> checkInRollCallById(@RequestParam("id") Integer id) {
        return success(clockInInfoService.checkInRollCallById(id));
    }

    @PostMapping("/checkInTraineeReport")
    @ApiOperation("移动端-确认报道签到")
    @PreAuthorize("@ss.hasPermission('edu:clock-in-info:query')")
    public CommonResult<Boolean> checkInTraineeReport(@RequestParam("id") Long id) {
        return success(clockInInfoService.checkInTraineeReportById(id));
    }

    @GetMapping("/student-clocking-status")
    @ApiOperation("查看学员考勤状态")
    @PreAuthorize("@ss.hasPermission('edu:clock-in-info:query')")
    public CommonResult<PageResult<ClockInInfoStudentStatusVO>> getStudentClockingStatusPage(HttpServletRequest request,@Valid ClockInInfoPageReqVO pageVO) {
        PageResult<ClockInInfoStudentStatusVO> pageResult = clockInInfoService.getStudentClockingStatusPage(request,pageVO);
        return success(pageResult);
    }

    @GetMapping("/clock-export-excel")
    @ApiOperation(value = "考情详情导出")
    @PreAuthorize("@ss.hasPermission('edu:clock-in-info:export')")
    public void exportClockInfoExcels(HttpServletRequest request,ClockInInfoExcelExportReqVO reqVO, HttpServletResponse response) throws IOException {
        List<ClockInfoExcelVO> list = clockInInfoService.getClockInfoList(request,reqVO);

        ExcelUtils.writeByIncludeColumnIndexes(response, "详情列表.xls",
                "数据", ClockInfoExcelVO.class, list, reqVO.getIncludeColumnIndexes());
    }


    @GetMapping("/myErrorClockInInfo")
    @ApiOperation("获得我的异常考勤")
    @PreAuthorize("@ss.hasPermission('edu:clock-in-info:query')")
    public CommonResult<List<MyErrorClockInInfoRespVO>> getMyErrorClockInInfo(MyErrorClockInInfoReqVO reqVO) {
        return success(clockInInfoService.getMyErrorClockInInfoList(reqVO));
    }

    @PostMapping("/generateRecords")
    @ApiOperation("生成每日考勤签到记录")
    public void generateRecords(){
        clockInInfoService.generateRecords();
    }


}

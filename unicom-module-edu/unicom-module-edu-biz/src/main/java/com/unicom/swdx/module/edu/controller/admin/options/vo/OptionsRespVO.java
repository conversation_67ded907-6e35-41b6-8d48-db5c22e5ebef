package com.unicom.swdx.module.edu.controller.admin.options.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 选项 Response VO")
@Data
@ExcelIgnoreUnannotated
public class OptionsRespVO {

    @Schema(description = "主键ID",  example = "29054")
    @ExcelProperty("主键ID")
    private Long id;

    @Schema(description = "选项类型(字典)", example = "1")
    @ExcelProperty("选项类型(字典)")
    private String optionsType;

    @Schema(description = "选项内容")
    @ExcelProperty("选项内容")
    private String content;

    @Schema(description = "选项分数")
    @ExcelProperty("选项分数")
    private BigDecimal score;

    @Schema(description = "创建部门")
    @ExcelProperty("创建部门")
    private Long createDept;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    @ExcelProperty("创建人")
    private Long creator;

    @Schema(description = "更新人")
    @ExcelProperty("更新人")
    private Long updater;

    @Schema(description = "删除标志")
    @ExcelProperty("删除标志")
    private Integer deleted;

    @Schema(description = "题干主键", example = "27397")
    @ExcelProperty("题干主键")
    private Long questionId;

    private Long selectCount;

    private BigDecimal percentage;

}

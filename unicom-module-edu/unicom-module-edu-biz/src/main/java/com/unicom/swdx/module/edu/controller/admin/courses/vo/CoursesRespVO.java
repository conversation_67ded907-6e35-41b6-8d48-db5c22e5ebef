package com.unicom.swdx.module.edu.controller.admin.courses.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

import static com.unicom.swdx.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@ApiModel("管理后台 - 课程库 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CoursesRespVO extends CoursesBaseVO {

    @ApiModelProperty(value = "序号")
    private Long serialNumber;

    @ApiModelProperty(value = "课程主键", required = true)
    private Long id;

    @ApiModelProperty(value = "授课教师姓名集合字符串")
    private String teacherNameList;

    @ApiModelProperty(value = "授课教师姓名列表")
    private List<String> teacherNameListList;

    @ApiModelProperty(value = "课程分类")
    private String theme;

    @ApiModelProperty(value = "教学形式")
    private String educateForm;

    @ApiModelProperty(value = "管理部门")
    private String managementDept;

    @ApiModelProperty(value = "授课教师id列表")
    private List<Long> teacherIdList;

    @ApiModelProperty(value = "授课教师id集合字符串")
    private String teacherIdListStr;

    @ApiModelProperty(value = "教学点名称")
    private String teachingPointName;

    @ApiModelProperty(value = "创建时间", required = true)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private String createTime;

}

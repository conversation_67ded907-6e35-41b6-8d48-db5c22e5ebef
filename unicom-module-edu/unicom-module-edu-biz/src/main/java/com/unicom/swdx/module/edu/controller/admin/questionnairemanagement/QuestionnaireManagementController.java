package com.unicom.swdx.module.edu.controller.admin.questionnairemanagement;

import com.unicom.swdx.framework.tenant.core.aop.NonRepeatSubmit;
import com.unicom.swdx.module.edu.controller.admin.questioncategorymanagement.vo.QuestionCategoryManagementListReqVO;
import com.unicom.swdx.module.edu.controller.admin.questioncategorymanagement.vo.QuestionCategoryManagementRespVO;
import com.unicom.swdx.module.edu.controller.admin.teacherinformation.vo.TeacherInformationExcelVO;
import com.unicom.swdx.module.edu.dal.dataobject.questioncategorymanagement.QuestionCategoryManagementDO;
import com.unicom.swdx.module.edu.utils.questionnairemanagement.ExcelExportHelper;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;
import java.util.stream.Collectors;

import com.unicom.swdx.framework.common.pojo.PageParam;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.util.object.BeanUtils;
import static com.unicom.swdx.framework.common.pojo.CommonResult.success;

import com.unicom.swdx.framework.excel.core.util.ExcelUtils;

import com.unicom.swdx.module.edu.controller.admin.questionnairemanagement.vo.*;
import com.unicom.swdx.module.edu.dal.dataobject.questionnairemanagement.QuestionnaireManagementDO;
import com.unicom.swdx.module.edu.service.questionnairemanagement.QuestionnaireManagementService;

@Tag(name = "管理后台 - 评估问卷管理")
@RestController
@RequestMapping("/edu/questionnaire-management")
@Validated
public class QuestionnaireManagementController {

    @Resource
    private QuestionnaireManagementService questionnaireManagementService;

    @PostMapping("/create")
    @Operation(summary = "创建评估问卷管理")
    @PreAuthorize("@ss.hasPermission('edu:questionnaire-management:create')")
    public CommonResult<Long> createQuestionnaireManagement(@Valid @RequestBody QuestionnaireManagementSaveReqVO createReqVO) {
        return success(questionnaireManagementService.createQuestionnaireManagement(createReqVO));
    }

    @PostMapping("/update")
    @Operation(summary = "更新评估问卷管理")
    @PreAuthorize("@ss.hasPermission('edu:questionnaire-management:update')")
    public CommonResult<Boolean> updateQuestionnaireManagement(@Valid @RequestBody QuestionnaireManagementSaveReqVO updateReqVO) {
        questionnaireManagementService.updateQuestionnaireManagement(updateReqVO);
        return success(true);
    }

    @PostMapping("/updateStatus")
    @Operation(summary = "更新问卷状态")
    @PreAuthorize("@ss.hasPermission('edu:questionnaire-management:update')")
    public CommonResult<Boolean> updateQuestionnaireManagementStatus(@Valid @RequestBody QuestionnaireManagementSaveReqVO updateReqVO) {
        questionnaireManagementService.updateQuestionnaireManagementStatus(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除评估问卷管理")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('edu:questionnaire-management:delete')")
    public CommonResult<Boolean> deleteQuestionnaireManagement(@RequestParam("id") Long id) {
        questionnaireManagementService.deleteQuestionnaireManagement(id);
        return success(true);
    }

    @PostMapping("/batchDelete")
    @Operation(summary = "批量删除评估问卷管理")
    @PreAuthorize("@ss.hasPermission('edu:questionnaire-management:delete')")
    public CommonResult<Boolean> deleteQuestionnaireManagement(@RequestParam("ids") List<Long> ids) {
        questionnaireManagementService.batchDeleteQuestionnaireManagement(ids);
        return success(true);
    }

    @PostMapping("/publish")
    @Operation(summary = "发布问卷")
    @NonRepeatSubmit(limitType = NonRepeatSubmit.Type.PARAM,lockTime = 15000)  //一分钟防止重复提交相同内容
    public CommonResult<Boolean> publishQuestionnaire(@Valid @RequestBody QuestionnairePublishVO publishVO) {
        questionnaireManagementService.publishQuestionnaireManagement(publishVO);
        return success(true);
    }

    @PostMapping("/cutoff")
    @Operation(summary = "截止问卷")
    public CommonResult<Boolean> cutoffQuestionnaire(@RequestParam("questionnaireId") Long questionnaireId) {
        questionnaireManagementService.cutoffQuestionnaire(questionnaireId);
        return success(true);
    }

    @PostMapping("/remind")
    @Operation(summary = "问卷提醒")
    public CommonResult<Boolean> questionnaireRemind(@Valid @RequestBody QuestionnaireRemindVO remindVO) {
        questionnaireManagementService.questionnaireRemind(remindVO);
        return success(true);
    }

    @GetMapping("/statistics")
    @Operation(summary = "统计分析")
    public CommonResult<QuestionnaireStatisticsRespVO> questionnaireStatistics(@RequestParam("id") Long id) {
        QuestionnaireStatisticsRespVO statistics = questionnaireManagementService.getQuestionnaireStatistics(id);
        return success(statistics);
    }

    @GetMapping("/statsPage")
    @Operation(summary = "获得评估问卷统计分页")
    @PreAuthorize("@ss.hasPermission('edu:questionnaire-management:query')")
    public CommonResult<PageResult<QuestionnaireStatsRespVO>> getQuestionnaireManagementStatsPage(@Valid QuestionnaireStatsPageReqVO pageReqVO) {
        PageResult<QuestionnaireStatsRespVO> pageResult = questionnaireManagementService.getQuestionnaireStatsPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/statsPageExport")
    @Operation(summary = "获得评估问卷统计分页")
    @PreAuthorize("@ss.hasPermission('edu:questionnaire-management:query')")
    public void exportQuestionnaireManagementStatsPage(@Valid QuestionnaireStatsPageReqVO pageReqVO, HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<QuestionnaireStatsRespVO> pageResult = questionnaireManagementService.getQuestionnaireStatsPage(pageReqVO).getList();
        if (pageReqVO.getSelectedIdList() != null && pageReqVO.getSelectedIdList() != "") {
            List<Long> ids = Arrays.stream(pageReqVO.getSelectedIdList().split(","))
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .map(Long::parseLong)
                    .collect(Collectors.toList());
            pageResult = pageResult.stream()
                    .filter(questionnaireStatsRespVO -> ids.contains(questionnaireStatsRespVO.getId()))
                    .collect(Collectors.toList());
        }
        // 导出 Excel
        ExcelUtils.writeByIncludeColumnIndexes(response, "评估问卷统计.xls",
                "数据", QuestionnaireStatsRespVO.class, null, pageResult, pageReqVO.getIncludeColumnIndexes());
    }



    @GetMapping("/getEvaluationCount")
    @Operation(summary = "获得评估问卷统计分页")
    @PreAuthorize("@ss.hasPermission('edu:questionnaire-management:query')")
    public CommonResult<PageResult<EvaluationCountRespVO>> getEvaluationCount(@Valid EvaluationCountPageReqVO pageReqVO) {
        PageResult<EvaluationCountRespVO> pageResult = questionnaireManagementService.getEvaluationCount(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/detail")
    @Operation(summary = "明细")
    public CommonResult<List<DetailRespVO>> questionnaireDetail(@Valid DetailPageReqVO pageReqVO) {
        List<DetailRespVO> result = questionnaireManagementService.getQuestionnaireDetail(pageReqVO);
        return success(result);
    }

    @GetMapping("/detail-export")
    @Operation(summary = "导出评估问卷明细 Excel")
    @PreAuthorize("@ss.hasPermission('edu:questionnaire-management:export')")

    public void exportQuestionnaireDetailExcel(@Valid DetailPageReqVO pageReqVO,
                                                   HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<DetailRespVO> result = questionnaireManagementService.getQuestionnaireDetail(pageReqVO);
        // 导出 Excel
        ExcelUtils.writeByIncludeColumnIndexes(response, "评估问卷管理.xls",
                "数据", DetailRespVO.class, null, result, pageReqVO.getIncludeColumnIndexes());
    }


    @GetMapping("/export")
    public void exportCustomQuestionnaireExcel(@Valid DetailPageReqVO pageReqVO, HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<DetailRespVO> result = questionnaireManagementService.getQuestionnaireDetail(pageReqVO);

        if (pageReqVO.getSelectedSerialList() != null && pageReqVO.getSelectedSerialList() != "") {
            List<Long> ids = Arrays.stream(pageReqVO.getSelectedSerialList().split(","))
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .map(Long::parseLong)
                    .collect(Collectors.toList());
            result = result.stream()
                    .filter(detailRespVO -> ids.contains(detailRespVO.getSerialNumber()))
                    .collect(Collectors.toList());
        }


        // 构造自定义表头和数据
        List<List<String>> heads = ExcelExportHelper.buildDynamicHeads(result);
        List<List<String>> data = ExcelExportHelper.buildDynamicData(result);

        // 输出 Excel
        ExcelExportHelper.writeDynamicHeadExcel(response, "评估问卷明细.xls", "问卷答卷", heads, data);
    }



    @GetMapping("/getPublishScale")
    @Operation(summary = "获取发布范围")
    public CommonResult<List<PublishScaleRespVO>> getPublishScale() {
        List<PublishScaleRespVO> publishScale = questionnaireManagementService.getPublishScale();
        return success(publishScale);
    }

    @GetMapping("/download-evaluation")
    @Operation(summary = "评估表下载")
    @PreAuthorize("@ss.hasPermission('edu:questionnaire-management:export')")

    public void downloadEvaluation(@Valid QuestionnaireManagementPageReqVO pageReqVO,
                                               HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<QuestionnaireManagementRespVO> list = questionnaireManagementService.getQuestionnaireManagementPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.writeByIncludeColumnIndexes(response, "评估问卷管理.xls",
                "数据", QuestionnaireManagementRespVO.class, null, list, pageReqVO.getIncludeColumnIndexes());
    }

    @GetMapping("/getCollectingEducateForm")
    @Operation(summary = "获取正在收集中的教学形式")
    @PreAuthorize("@ss.hasPermission('edu:questionnaire-management:delete')")
    public CommonResult<List<Long>> getCollectingEducateForm() {
       return success(questionnaireManagementService.getCollectingEducateForm());
    }

    @GetMapping("/get")
    @Operation(summary = "获得评估问卷管理")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('edu:questionnaire-management:query')")
    public CommonResult<QuestionnaireManagementRespVO> getQuestionnaireManagement(@RequestParam("id") Long id,
                                                                                  @RequestParam(value = "isEvaluate", required = false, defaultValue = "false") Boolean isEvaluate) {
        QuestionnaireManagementRespVO questionnaireManagement = questionnaireManagementService.getQuestionnaireManagement(id, isEvaluate);
        return success(questionnaireManagement);
    }

    @GetMapping("/getTemplate")
    @Operation(summary = "获得评估问卷管理")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('edu:questionnaire-management:query')")
    public CommonResult<List<QuestionnaireManagementRespVO>> getTemplateQuestionnaire(@RequestParam("templateType") Integer templateType,
                                                                                      @RequestParam(value = "questionnaireId", required = false) Long questionnaireId) {
        List<QuestionnaireManagementRespVO> questionnaireManagement = questionnaireManagementService.getTemplateQuestionnaire(templateType, questionnaireId);
        return success(questionnaireManagement);
    }

    @GetMapping("/getRateResult")
    @Operation(summary = "获得已评的问卷")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('edu:questionnaire-management:query')")
    public CommonResult<QuestionnaireManagementRespVO> getRatedQuestionnaire(@RequestParam("questionnaireId") Long questionnaireId,
                                                                             @RequestParam(value = "classCourseId", required = false) Long classCourseId,
                                                                             @RequestParam(value = "userId", required = false) Long userId) {
        QuestionnaireManagementRespVO questionnaireManagement = questionnaireManagementService.getRatedQuestionnaireManagement(questionnaireId,userId, classCourseId);
        return success(questionnaireManagement);
    }

    @GetMapping("/questionList")
    @Operation(summary = "获得题库列表")
    @PreAuthorize("@ss.hasPermission('edu:question-category-management:query')")
    public CommonResult<List<QuestionRespVO>> getQuestionCategoryManagementList(@Valid QuestionListReqVO listReqVO) {
        List<QuestionRespVO> list = questionnaireManagementService.getQuestionList(listReqVO);
        return success(list);
    }

    @GetMapping("/page")
    @Operation(summary = "获得评估问卷管理分页")
    @PreAuthorize("@ss.hasPermission('edu:questionnaire-management:query')")
    public CommonResult<PageResult<QuestionnaireManagementRespVO>> getQuestionnaireManagementPage(@Valid QuestionnaireManagementPageReqVO pageReqVO) {
        PageResult<QuestionnaireManagementRespVO> pageResult = questionnaireManagementService.getQuestionnaireManagementPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出评估问卷管理 Excel")
    @PreAuthorize("@ss.hasPermission('edu:questionnaire-management:export')")

    public void exportQuestionnaireManagementExcel(@Valid QuestionnaireManagementPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<QuestionnaireManagementRespVO> list = questionnaireManagementService.getQuestionnaireManagementPage(pageReqVO).getList();
        if (pageReqVO.getSelectedIdList() != null && pageReqVO.getSelectedIdList() != "") {
            List<Long> ids = Arrays.stream(pageReqVO.getSelectedIdList().split(","))
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .map(Long::parseLong)
                    .collect(Collectors.toList());
            list = list.stream()
                    .filter(questionnaireManagementRespVO -> ids.contains(questionnaireManagementRespVO.getId()))
                    .collect(Collectors.toList());
        }
        // 导出 Excel
        ExcelUtils.writeByIncludeColumnIndexes(response, "评估问卷管理.xls",
                "数据", QuestionnaireManagementRespVO.class, null, list, pageReqVO.getIncludeColumnIndexes());
    }

    @GetMapping("/evaluation-export")
    @Operation(summary = "导出评估问卷明细 Excel")
    @PreAuthorize("@ss.hasPermission('edu:questionnaire-management:export')")
    public void exportEvaluationExcel(@Valid DetailPageReqVO pageReqVO, HttpServletResponse response){

        questionnaireManagementService.exportEvaluationExcel();

    }
}

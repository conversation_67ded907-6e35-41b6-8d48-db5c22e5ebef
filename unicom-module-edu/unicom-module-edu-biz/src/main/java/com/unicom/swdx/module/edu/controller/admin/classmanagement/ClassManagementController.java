package com.unicom.swdx.module.edu.controller.admin.classmanagement;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.unicom.swdx.framework.common.pojo.CommonResult;
import com.unicom.swdx.framework.common.pojo.PageParam;
import com.unicom.swdx.framework.common.pojo.PageResult;
import com.unicom.swdx.framework.common.util.validation.ExcelValidator;
import com.unicom.swdx.framework.excel.core.util.ExcelUtils;
import com.unicom.swdx.framework.operatelog.core.annotations.OperateLog;
import com.unicom.swdx.framework.tenant.core.aop.TenantIgnore;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.excelimporthandler.*;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.*;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.businesscenter.ClassManagementSimpleForBusinessCenterReqVO;
import com.unicom.swdx.module.edu.controller.admin.classmanagement.vo.businesscenter.ClassManagementSimpleForBusinessCenterRespVO;
import com.unicom.swdx.module.edu.convert.classmanagement.ClassManagementConvert;
import com.unicom.swdx.module.edu.dal.dataobject.classmanagement.ClassManagementDO;
import com.unicom.swdx.module.edu.dal.mysql.classmanagement.ClassManagementMapper;
import com.unicom.swdx.module.edu.enums.classmanagement.ClassAttributeDictEnum;
import com.unicom.swdx.module.edu.service.classmanagement.ClassManagementService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.Normalizer;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.unicom.swdx.framework.common.enums.ErrorCodeConstants.EXPORT_FAILED;
import static com.unicom.swdx.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.unicom.swdx.framework.common.pojo.CommonResult.success;
import static com.unicom.swdx.framework.operatelog.core.enums.OperateTypeEnum.IMPORT;
import static com.unicom.swdx.module.edu.enums.ErrorCodeConstants.CLASS_MANAGEMENT_HEAD_NAME_ERROR;

@Api(tags = "管理后台 - 班次管理")
@RestController
@RequestMapping("/edu/class-management")
@Validated
public class ClassManagementController {

    @Resource
    private ClassManagementService classManagementService;
    @Resource
    private ClassManagementMapper classManagementMapper;

    private static final String EXPECTED_TEMPLATE_NAME = "班级导入模版";

    @PostMapping("/create")
    @ApiOperation("新增")
    @PreAuthorize("@ss.hasPermission('edu:class-management:create')")
    public CommonResult<Long> createClassManagement(@Valid @RequestBody ClassManagementCreateReqVO createReqVO) {
        return success(classManagementService.createClassManagement(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation("编辑")
    @PreAuthorize("@ss.hasPermission('edu:class-management:update')")
    public CommonResult<Boolean> updateClassManagement(@Valid @RequestBody ClassManagementUpdateReqVO updateReqVO) {
        classManagementService.updateClassManagement(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @ApiOperation("删除")
    @ApiImplicitParam(name = "id", value = "编号", required = true, dataTypeClass = Integer.class)
    @PreAuthorize("@ss.hasPermission('edu:class-management:delete')")
    public CommonResult<Boolean> deleteClassManagement(@RequestParam("id") Long id) {
        classManagementService.deleteClassManagement(id);
        return success(true);
    }

    @PostMapping("/delete-batch")
    @ApiOperation("批量删除")
    @PreAuthorize("@ss.hasPermission('edu:class-management:delete')")
    public CommonResult<Boolean> deleteClassManagerDeleteBatch(@Valid @RequestBody ClassManagerDeleteVO classManagerDeleteVO) {
        classManagementService.deleteClassManagerDeleteBatch(classManagerDeleteVO);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation("获得单个数据列表")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Integer.class)
    @PermitAll
    public CommonResult<ClassManagementRespVO> getClassManagement(@RequestParam("id") Long id) {
        ClassManagementDO classManagement = classManagementService.getClassManagement(id);
        return success(ClassManagementConvert.INSTANCE.convert(classManagement));
    }

    @GetMapping("/list")
    @ApiOperation("获得多个数据列表")
    @ApiImplicitParam(name = "ids", value = "编号列表", required = true, example = "1024,2048", dataTypeClass = List.class)
    @PreAuthorize("@ss.hasPermission('edu:class-management:query')")
    public CommonResult<List<ClassManagementRespVO>> getClassManagementList(@RequestParam("ids") Collection<Integer> ids) {
        List<ClassManagementDO> list = classManagementService.getClassManagementList(ids);
        return success(ClassManagementConvert.INSTANCE.convertList(list));
    }


    //给业中单独调的接口
    @GetMapping("/listSingle")
    @ApiOperation("获得多个数据列表")
    @ApiImplicitParam(name = "ids", value = "编号列表", required = true, example = "1024,2048", dataTypeClass = List.class)
    @PermitAll
    @TenantIgnore
    public CommonResult<List<ClassManagementRespVO>> getClassManagementListSingle(@RequestParam("ids") Collection<Integer> ids) {
        List<ClassManagementDO> list = classManagementService.getClassManagementList(ids);
        return success(ClassManagementConvert.INSTANCE.convertList(list));
    }



    @GetMapping("/page")
    @ApiOperation("分页列表")
    @PreAuthorize("@ss.hasPermission('edu:class-management:query')")
    public CommonResult<PageResult<ClassManagementRespVO>> getClassManagementPage(HttpServletRequest request, @Valid ClassManagementPageReqVO pageVO) {
        PageResult<ClassManagementRespVO> pageResult = classManagementService.getClassManagementPage(request,pageVO);
//        PageResult<ClassManagementRespVO> classManagementRespVOPageResult = ClassManagementConvert.INSTANCE.convertPage(pageResult);
        return success(pageResult);
    }

    @GetMapping("/allList")
    @ApiOperation("列表")
    @PreAuthorize("@ss.hasPermission('edu:class-management:query')")
    public CommonResult<List<ClassManagementRespVO>> getClassManagementAllList(HttpServletRequest request, @Valid ClassManagementPageReqVO pageVO) {
        pageVO.setPageNo(1);
        pageVO.setPageSize(Integer.MAX_VALUE);
        PageResult<ClassManagementRespVO> pageResult = classManagementService.getClassManagementPage(request,pageVO);
//        PageResult<ClassManagementRespVO> classManagementRespVOPageResult = ClassManagementConvert.INSTANCE.convertPage(pageResult);
        return success(pageResult.getList());
    }


    @PostMapping("/simpleListForBusinessCenter")
    @ApiOperation("业中首页-仪表盘-考勤三率班级下钻")
    @PreAuthorize("@ss.hasPermission('edu:class-management:query')")
    @PermitAll
    @TenantIgnore
    public CommonResult<List<ClassManagementSimpleForBusinessCenterRespVO>> simpleListForBusinessCenter(@Valid @RequestBody ClassManagementSimpleForBusinessCenterReqVO reqVO) {
        List<ClassManagementSimpleForBusinessCenterRespVO> resList = classManagementService.simpleListForBusinessCenter(reqVO);
        return success(resList);
    }

    @PostMapping("/pageForElectiveReleaseCreate")
    @ApiOperation("创建选修课发布-根据上课时间段选择班级范围分页列表")
    @PreAuthorize("@ss.hasPermission('edu:class-management:query')")
    public CommonResult<PageResult<ClassManagementElectiveReleaseRespVO>> getPageForElectiveReleaseCreate(@Valid @RequestBody ClassManagementElectiveReleasePageReqVO reqVO) {
        PageResult<ClassManagementElectiveReleaseRespVO> pageResult = classManagementService.getPageForElectiveReleaseCreate(reqVO);
        return success(pageResult);
    }

    @PostMapping("/update-sort")
    @ApiOperation("更新排序")
    @PreAuthorize("@ss.hasPermission('edu:class-management:update')")
    public CommonResult<Boolean> updateClassManagementSort(@RequestParam("id") Long id, @RequestParam("sort") Integer sort) {
        classManagementService.updateClassManagementSort(id, sort);
        return success(true);
    }

    @PostMapping("/publish-batch")
    @ApiOperation("批量发布")
    @PreAuthorize("@ss.hasPermission('edu:class-management:update')")
    public CommonResult<Boolean> updateClassManagementPublish(@Valid @RequestBody ClassManagerDeleteVO classManagerDeleteVO) {
        classManagementService.updateClassManagementPublish(classManagerDeleteVO);
        return success(true);
    }

    @GetMapping("/export-excel")
    @ApiOperation(value = "班级管理导出-增加办班类型")
    @PreAuthorize("@ss.hasPermission('edu:class-management:export')")
    public void exportClassManagementExcel(ClassManagementExportParamsVO reqVO, HttpServletResponse response) throws IOException {
        List<ClassManagementExcelVO> list = classManagementService.getClassManagementInfoList(reqVO);
        List<ClassManagementExportExcelVO> list1 = ClassManagementConvert.INSTANCE.convertList1(list);

        ExcelUtils.writeByIncludeColumnIndexes(response, "班级列表.xls",
                "数据", ClassManagementExportExcelVO.class, list1, reqVO.getIncludeColumnIndexes());
    }

    @GetMapping("/class-export-excel")
    @ApiOperation(value = "班级管理导出")
    @PreAuthorize("@ss.hasPermission('edu:class-management:export')")
    public void exportClassManagementExcels(ClassManagementExportParamsVO reqVO, HttpServletResponse response) throws IOException {
        List<ClassManagementExcelVO> list = classManagementService.getClassManagementInfoList(reqVO);

        ExcelUtils.writeByIncludeColumnIndexes(response, "班级列表.xls",
                "数据", ClassManagementExcelVO.class, list, reqVO.getIncludeColumnIndexes());
    }

    @GetMapping("/get-import-templates")
    @ApiOperation("获得主体班导入班级信息模板")
    @TenantIgnore
    public void importTemplate(HttpServletResponse response) throws IOException {
        // 输出
        String filename = "主体班次导入模板.xls";
        String sheetName = "班级信息列表";
        response.setHeader("Access-Control-Expose-Headers","Content-Disposition");
        response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(filename, "UTF-8"));
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        List<ClassInfoImportExcelVO> classInfoImportExcelVOS = new ArrayList<>();
        ClassInfoImportExcelVO classInfoImportExcelVO = new ClassInfoImportExcelVO();
        classInfoImportExcelVO.setISort("示例：1");
        classInfoImportExcelVO.setClassName("测试班级");
        classInfoImportExcelVO.setClassType("基本培训班");
        classInfoImportExcelVO.setYear("2024");
        classInfoImportExcelVO.setSemester("秋季学期");
        classInfoImportExcelVO.setLearningSystem("4");
        classInfoImportExcelVO.setTrainingObject("中青年干部");
        classInfoImportExcelVO.setPeopleNumber("30");
        classInfoImportExcelVO.setTurn("委托单位");
        classInfoImportExcelVO.setCampus("韶山校区");
        classInfoImportExcelVO.setReportingTime("2024/12/17");
        classInfoImportExcelVO.setClassOpenTime("2024/12/28");
        classInfoImportExcelVO.setCompletionTime("2024/12/29");
        classInfoImportExcelVO.setRegistrationStartTime("2024/12/11");
        classInfoImportExcelVO.setRegistrationEndTime("2024/12/12");
        classInfoImportExcelVO.setPaymentReport("否");
        classInfoImportExcelVO.setEvaluate("是");
        classInfoImportExcelVO.setRegionType("省内");
        classInfoImportExcelVO.setReportPeriod("上午");
        classInfoImportExcelVO.setAccommodationLocation("-");
        classInfoImportExcelVO.setTransferLocation("-");
        classInfoImportExcelVO.setStationTransfer("否");
        classInfoImportExcelVO.setSort("1");
        classInfoImportExcelVO.setRemark("备注说明");
        classInfoImportExcelVO.setReportPeriod("11:00-12:00");
        classInfoImportExcelVO.setReturnPeriod("15:00-16:00");
        classInfoImportExcelVO.setProgramInitiator("开班领导");
        classInfoImportExcelVO.setProgramCloser("结业领导");
        classInfoImportExcelVOS.add(classInfoImportExcelVO);
        // 输出 Excel
        try {
            EasyExcel.write(response.getOutputStream(), ClassInfoImportExcelVO.class)
                    .autoCloseStream(false) // 不要自动关闭，交给 Servlet 自己处理
                    .registerWriteHandler(new GetCampusSheetWriteHandler(classManagementMapper))
                    .registerWriteHandler(new GetClassTypeSheetWriteHandler(classManagementMapper))
                    .registerWriteHandler(new GetEvaluateSheetWriteHandler())
                    .registerWriteHandler(new GetPaymentSheetWriteHandler())
//                    .registerWriteHandler(new GetTurnSheetWriteHandler())
                    .registerWriteHandler(new GetRegionTypeSheetWriteHandler())
//                    .registerWriteHandler(new GetReportPeriodSheetWriteHandler())
                    .registerWriteHandler(new GetStationTransferSheetWriteHandler())
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()) // 基于 column 长度，自动适配。最大 255 宽度
                    .sheet(sheetName).doWrite(classInfoImportExcelVOS);
        } catch (IOException e) {
            response.setContentType("application/json;charset=UTF-8");
            throw exception(EXPORT_FAILED);
        }
    }

    @GetMapping("/get-import-not-main-templates")
    @ApiOperation("获得委托班班导入班级信息模板")
    @TenantIgnore
    public void importNotMainTemplate(HttpServletResponse response) throws IOException {
        // 输出
        String filename = "委托班次导入模板.xls";
        String sheetName = "班级信息列表";
        response.setHeader("Access-Control-Expose-Headers","Content-Disposition");
        response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(filename, "UTF-8"));
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        List<ClassInfoImportNotMainExcelVO> classInfoImportExcelVOS = new ArrayList<>();
        ClassInfoImportNotMainExcelVO classInfoImportExcelVO = new ClassInfoImportNotMainExcelVO();
        classInfoImportExcelVO.setISort("示例：1");
        classInfoImportExcelVO.setClassName("测试班级");
        classInfoImportExcelVO.setClassType("基本培训班");
        classInfoImportExcelVO.setYear("2024");
        classInfoImportExcelVO.setSemester("秋季学期");
        classInfoImportExcelVO.setLearningSystem("4");
        classInfoImportExcelVO.setTrainingObject("中青年干部");
        classInfoImportExcelVO.setPeopleNumber("30");
        classInfoImportExcelVO.setTurn("委托单位");
        classInfoImportExcelVO.setCampus("韶山校区");
        classInfoImportExcelVO.setReportingTime("2024/12/17");
        classInfoImportExcelVO.setClassOpenTime("2024/12/28");
        classInfoImportExcelVO.setCompletionTime("2024/12/29");
        classInfoImportExcelVO.setRegistrationStartTime("2024/12/11");
        classInfoImportExcelVO.setRegistrationEndTime("2024/12/12");
        classInfoImportExcelVO.setPaymentReport("否");
        classInfoImportExcelVO.setEvaluate("是");
        classInfoImportExcelVO.setRegionType("省内");
        classInfoImportExcelVO.setReportPeriod("上午");
        classInfoImportExcelVO.setAccommodationLocation("-");
        classInfoImportExcelVO.setTransferLocation("-");
        classInfoImportExcelVO.setStationTransfer("否");
        classInfoImportExcelVO.setSort("1");
        classInfoImportExcelVO.setRemark("备注说明");
        classInfoImportExcelVO.setReportPeriod("11:00-12:00");
        classInfoImportExcelVO.setReturnPeriod("15:00-16:00");
        classInfoImportExcelVO.setProgramInitiator("开班领导");
        classInfoImportExcelVO.setProgramCloser("结业领导");
        classInfoImportExcelVOS.add(classInfoImportExcelVO);
        // 输出 Excel
        try {
            EasyExcel.write(response.getOutputStream(), ClassInfoImportNotMainExcelVO.class)
                    .autoCloseStream(false) // 不要自动关闭，交给 Servlet 自己处理
                    .registerWriteHandler(new GetCampusSheetWriteHandler(classManagementMapper))
                    .registerWriteHandler(new GetClassTypeSheetWriteHandler(classManagementMapper))
                    .registerWriteHandler(new GetEvaluateSheetWriteHandler())
                    .registerWriteHandler(new GetPaymentSheetWriteHandler())
//                    .registerWriteHandler(new GetTurnSheetWriteHandler())
                    .registerWriteHandler(new GetRegionTypeSheetWriteHandler())
//                    .registerWriteHandler(new GetReportPeriodSheetWriteHandler())
                    .registerWriteHandler(new GetStationTransferSheetWriteHandler())
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()) // 基于 column 长度，自动适配。最大 255 宽度
                    .sheet(sheetName).doWrite(classInfoImportExcelVOS);
        } catch (IOException e) {
            response.setContentType("application/json;charset=UTF-8");
            throw exception(EXPORT_FAILED);
        }
    }

    @PostMapping("/import")
    @ApiOperation("导入主体班级信息")
    @OperateLog(type = IMPORT)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "Excel 文件", required = true, dataTypeClass = MultipartFile.class)
    })
    @PreAuthorize("@ss.hasPermission('edu:class-management:import')")
    public CommonResult<ClassManagementImportRespVO> importExcel(@RequestParam("file") MultipartFile file) throws Exception {

        // 获取文件名并去除路径和扩展名
        String originalFileName = file.getOriginalFilename();

        if (originalFileName == null || originalFileName.isEmpty()) {
            throw new IllegalArgumentException("文件名为空");
        }

        // 获取文件名（不带路径和扩展名）
        String fileNameWithoutExtension = new File(originalFileName).getName().substring(0, originalFileName.lastIndexOf('.')).trim();

        // 标准化处理
        fileNameWithoutExtension = Normalizer.normalize(fileNameWithoutExtension, Normalizer.Form.NFC);

        // 调试输出
//        System.out.println("File Name: " + fileNameWithoutExtension);
//        System.out.println("Expected Template Name: " + EXPECTED_TEMPLATE_NAME);

        // 校验文件名是否等于预期的模板文件名
//        if (!EXPECTED_TEMPLATE_NAME.equals(fileNameWithoutExtension)) {
//            throw exception(CLASS_MANAGEMENT_FILE_NAME_ERROR);
//        }



        // 读取 Excel 文件，转换为列表对象
//        List<ClassInfoImportStrExcelVO> list = ExcelUtils.read(file, ClassInfoImportStrExcelVO.class);

        List<ClassInfoImportExcelVO> listMain = null;
        // 校验表头信息
        try{
            listMain = ExcelUtils.readFirstSheetAndCheckHead(file, ClassInfoImportExcelVO.class);
        }catch (Exception e){
            throw exception(CLASS_MANAGEMENT_HEAD_NAME_ERROR);
        }

        List<ClassInfoImportStrExcelVO> list = ClassManagementConvert.INSTANCE.convertList03(listMain);
        list.forEach(o->{
            o.setClassAttribute(ClassAttributeDictEnum.MAIN_CLASS.getDesc());
        });

        // 验证 Excel 数据
        ExcelValidator.valid(list,1);

        // 调用服务层，导入班次信息
        return success(classManagementService.importClassInfo(list, ClassAttributeDictEnum.MAIN_CLASS.getCode()));
    }

    @PostMapping("/importNotMain")
    @ApiOperation("导入委托班班级信息")
    @OperateLog(type = IMPORT)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "Excel 文件", required = true, dataTypeClass = MultipartFile.class)
    })
    @PreAuthorize("@ss.hasPermission('edu:class-management:import')")
    public CommonResult<ClassManagementImportRespVO> importNotMainExcel(@RequestParam("file") MultipartFile file) throws Exception {

        // 获取文件名并去除路径和扩展名
        String originalFileName = file.getOriginalFilename();

        if (originalFileName == null || originalFileName.isEmpty()) {
            throw new IllegalArgumentException("文件名为空");
        }

        // 获取文件名（不带路径和扩展名）
        String fileNameWithoutExtension = new File(originalFileName).getName().substring(0, originalFileName.lastIndexOf('.')).trim();

        // 标准化处理
        fileNameWithoutExtension = Normalizer.normalize(fileNameWithoutExtension, Normalizer.Form.NFC);

        // 调试输出
//        System.out.println("File Name: " + fileNameWithoutExtension);
//        System.out.println("Expected Template Name: " + EXPECTED_TEMPLATE_NAME);

        // 校验文件名是否等于预期的模板文件名
//        if (!EXPECTED_TEMPLATE_NAME.equals(fileNameWithoutExtension)) {
//            throw exception(CLASS_MANAGEMENT_FILE_NAME_ERROR);
//        }



        // 读取 Excel 文件，转换为列表对象
//        List<ClassInfoImportStrExcelVO> list = ExcelUtils.read(file, ClassInfoImportStrExcelVO.class);

        List<ClassInfoImportNotMainExcelVO> listMain = null;
        // 校验表头信息
        try{
            listMain = ExcelUtils.readFirstSheetAndCheckHead(file, ClassInfoImportNotMainExcelVO.class);
        }catch (Exception e){
            throw exception(CLASS_MANAGEMENT_HEAD_NAME_ERROR);
        }

        List<ClassInfoImportStrExcelVO> list = ClassManagementConvert.INSTANCE.convertList04(listMain);
        list.forEach(o->{
            o.setClassAttribute(ClassAttributeDictEnum.NOT_MAIN_CLASS.getDesc());
        });

        // 验证 Excel 数据
        ExcelValidator.valid(list,1);

        // 调用服务层，导入班次信息
        return success(classManagementService.importClassInfo(list, ClassAttributeDictEnum.NOT_MAIN_CLASS.getCode()));
    }


    @GetMapping("/page-applet")
    @ApiOperation("小程序班级切换")
    @PreAuthorize("@ss.hasPermission('edu:class-management:query')")
    public CommonResult<List<ClassManagementDO>> getClassManagementPageApplet(@Valid ClassManagementPageReqVO pageVO) {

        List<ClassManagementDO> result = classManagementService.getClassManagementPageApplet(pageVO);
        return success(result);
    }


    @GetMapping("/getClass")
    @ApiOperation("根据开班状态和教师id获取班级列表")
    @PreAuthorize("@ss.hasPermission('edu:class-management:query')")
    public CommonResult<List<ClassInfoRespVO>> getClassByStatus(@RequestParam("status") Integer status,
                                                                @RequestParam(value = "teacherId", required = false) Long teacherId,
                                                                @RequestParam(value = "clockIn", required = false) Integer clockIn,
                                                                @RequestParam(value = "classAttribute", required = false) Integer classAttribute) {
        List<ClassInfoRespVO> result = classManagementService.getClassPageByStatus(status,teacherId, clockIn,classAttribute);
        return success(result);
    }

    @GetMapping("/get-default-class")
    @ApiOperation("根据教师id获取默认班级")
    @PreAuthorize("@ss.hasPermission('edu:class-management:query')")
    public CommonResult<List<String>> getDefaultClassByTeacherId(@RequestParam(value = "teacherId" ,required = false) Long teacherId,
                                                                 @RequestParam(value = "clockIn", required = false) Integer clockIn) {
        List<String> result = classManagementService.getDefaultClassByTeacherId(teacherId, clockIn);
        if (Objects.isNull(result)){
            return success(Collections.emptyList());
        }
        return success(result);
    }


    @PostMapping("/clocking-in-rule")
    @ApiOperation("班级考勤规则设置")
    @PreAuthorize("@ss.hasPermission('edu:class-management:update')")
    public CommonResult<Boolean> updateClassClockingInRule(@Valid @RequestBody ClassClockingInUpdateReqVO clockingInUpdateReqVO) {
        classManagementService.updateClassClockingInRule(clockingInUpdateReqVO);
        return success(true);
    }

    @PostMapping("/class-sign-up")
    @ApiOperation("名额分配")
    @PreAuthorize("@ss.hasPermission('edu:class-management:create')")
    public CommonResult<Integer> createClassSignUp(@Valid @RequestBody List<ClassSignUpUnitBaseVO> classSignUpUnitBaseVO) {
        return success(classManagementService.createClassSignUp(classSignUpUnitBaseVO));
    }

    @GetMapping("/getSimple")
    @ApiOperation("获得班级信息")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Integer.class)
    @PreAuthorize("@ss.hasPermission('edu:class-management:query')")
    public CommonResult<ClassSimpleInfo> getSimpleClassInfo(@RequestParam("id") Long id) {
        ClassSimpleInfo classInfo = classManagementService.getSimpleClassInfo(id);
        return success(classInfo);
    }

    @GetMapping("/getSimpleList")
    @ApiOperation("获得班级信息列表")
    @PreAuthorize("@ss.hasPermission('edu:class-management:query')")
    public CommonResult<List<ClassSimpleInfo>> getSimpleClassInfo(@RequestParam(value = "teacherId", required = false) Long teacherId,
                                                            @RequestParam(value = "className", required = false) String className) {
        List<ClassSimpleInfo> list = classManagementService.getSimpleClassInfoList(teacherId, className);
        return success(list);
    }

    @GetMapping("/classHasCourseList")
    @ApiOperation("首页-班次课表-列表")
    @PreAuthorize("@ss.hasPermission('edu:class-management:query')")
    public CommonResult<PageResult<ClassHaveCourseVO>> getClassHasCourseList(@RequestParam(value = "className", required = false) String className , PageParam pageVO) {
        List<ClassHaveCourseVO> list = classManagementService.getClassHasCourseList(className);
//        list = list == null ? new ArrayList<>() : list;
        // 手动计算分页
        int total = list.size();
        int pageNo = pageVO.getPageNo();
        int pageSize = pageVO.getPageSize();
        // 计算分页起始索引和结束索引
        int fromIndex = Math.min((pageNo - 1) * pageSize, total);
        int toIndex = Math.min(fromIndex + pageSize, total);
        // 获取分页后的子列表
        List<ClassHaveCourseVO> pagedList = list.subList(fromIndex, toIndex);
        return success(new PageResult<>(pagedList, (long)total));
    }

    @GetMapping("/stat")
    @ApiOperation("首页-调训统计")
    @PreAuthorize("@ss.hasPermission('edu:class-management:query')")
    public CommonResult<StatRespVO> getStat() {
        StatRespVO result = classManagementService.getStat();
        return success(result);
    }

    @PostMapping("/completion-template")
    @ApiOperation("选择结业模版")
    @PreAuthorize("@ss.hasPermission('edu:class-management:update')")
    public CommonResult<Boolean> updateCompletionTemplate(@Valid @RequestBody ClassCompletionUpdateReqVO classCompletionUpdateReqVO) {
        classManagementService.updateCompletionTemplate(classCompletionUpdateReqVO);
        return success(true);
    }

    @GetMapping("/getClassClockAll")
    @ApiOperation("根据开班状态和教师id获取班级列表")
    @PreAuthorize("@ss.hasPermission('edu:class-management:query')")
    public CommonResult<List<ClassManagementDO>> getClassClockAll(@RequestParam(value = "className", required = false) String className,
                                                                  @RequestParam(value = "classAttribute", required = false) Integer classAttribute) {
        List<ClassInfoRespVO> list1 = classManagementService.getClassPageByStatus(2,null, 1,null);
        List<ClassInfoRespVO> list2 = classManagementService.getClassPageByStatus(4,null, 1,null);
        List<ClassInfoRespVO> mergedList = Stream.concat(list1.stream(), list2.stream())
                .collect(Collectors.toList());
        if(className!=null){
            mergedList=mergedList.stream().filter(s -> s.getName().contains(className)).collect(Collectors.toList());
        }

        List<Long> ids = mergedList.stream().map(ClassInfoRespVO::getId).collect(Collectors.toList());
        List<Integer> integers = ids.stream().map(Long::intValue).collect(Collectors.toList());
        List<ClassManagementDO> list=new ArrayList<>();
        if(integers.size()>0){
            list = classManagementService.getClassManagementList(integers);
            if(classAttribute!=null){
                list=list.stream().filter(l->l.getClassAttribute()!=null&&l.getClassAttribute().equals(classAttribute)).collect(Collectors.toList());
            }
            list=list.stream().sorted(Comparator.comparing(ClassManagementDO::getCreateTime).reversed()).collect(Collectors.toList());
        }

        return success(list);
    }

    @PostMapping("/sendMessage")
    @ApiOperation("短信通知")
    @PreAuthorize("@ss.hasPermission('edu:class-management:update')")
    public CommonResult<Boolean> classSendMessage(@Valid @RequestBody ClassManagementSendMessageRequestVO classManagementSendMessageRequestVO) {

        return success(classManagementService.classSendMessage(classManagementSendMessageRequestVO));
    }

}
